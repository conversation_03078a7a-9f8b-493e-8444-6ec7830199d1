'use server';

import { revalidatePath } from 'next/cache';
import { createServerAction } from 'zsa';

import prisma from '@/lib/prisma';

import {
  createDebtorSchema,
  updateDebtorSchema,
  debtorSchema,
  debtorSummarySchema,
  getAllDebtorsSchema,
  getDebtorByIdSchema,
  deleteDebtorSchema,
  toggleDebtorStatusSchema,
} from './schemas';

export const getAllDebtors = createServerAction()
  .output(getAllDebtorsSchema)
  .handler(async () => {
    return prisma.debtor.findMany({
      include: {
        cases: true,
        debts: {
          include: {
            creditor: {
              select: {
                name: true,
              },
            },
          },
        },
        assets: true,
        _count: {
          select: {
            cases: true,
            debts: true,
            assets: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });
  });

export const getDebtorById = createServerAction()
  .input(getDebtorByIdSchema)
  .output(debtorSchema)
  .handler(async ({ input: id }) => {
    const debtor = await prisma.debtor.findUnique({
      where: { id },
      include: {
        cases: true,
        debts: {
          include: {
            creditor: {
              select: {
                name: true,
              },
            },
          },
        },
        assets: true,
        _count: {
          select: {
            cases: true,
            debts: true,
            assets: true,
          },
        },
      },
    });

    if (!debtor) {
      throw new Error('Deudor no encontrado');
    }

    return debtor;
  });

export const createDebtor = createServerAction()
  .input(createDebtorSchema)
  .output(debtorSummarySchema)
  .handler(async ({ input }) => {
    const debtor = await prisma.debtor.create({
      data: input,
    });

    revalidatePath('/debtors');

    return debtor;
  });

export const updateDebtor = createServerAction()
  .input(updateDebtorSchema)
  .output(debtorSummarySchema)
  .handler(async ({ input: { id, ...rest } }) => {
    const updatedDebtor = await prisma.debtor.update({
      where: { id },
      data: rest,
    });

    revalidatePath('/debtors');

    return updatedDebtor;
  });

export const deleteDebtor = createServerAction()
  .input(deleteDebtorSchema)
  .output(debtorSummarySchema)
  .handler(async ({ input: id }) => {
    const deletedDebtor = await prisma.debtor.delete({
      where: { id },
    });

    revalidatePath('/debtors');

    return deletedDebtor;
  });

export const toggleDebtorStatus = createServerAction()
  .input(toggleDebtorStatusSchema)
  .output(debtorSummarySchema)
  .handler(async ({ input: id }) => {
    const debtor = await prisma.debtor.findUnique({
      where: { id },
      select: { status: true },
    });

    if (!debtor) {
      throw new Error('Deudor no encontrado');
    }

    const newStatus = debtor.status === 'Activo' ? 'Inactivo' : 'Activo';

    const updatedDebtor = await prisma.debtor.update({
      where: { id },
      data: { status: newStatus },
    });

    revalidatePath('/debtors');

    return updatedDebtor;
  });
