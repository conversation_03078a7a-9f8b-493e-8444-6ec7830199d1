'use client';

import {
  Eye,
  Edit,
  Trash2,
  Plus,
  Search,
  Download,
  MapPin,
  Mail,
  Phone,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { formatCurrency } from '@/lib/utils';

import { CreateDebtorDialog } from './create-debtor-dialog';
import { DeleteDebtorDialog } from './delete-debtor-dialog';
import { EditDebtorDialog } from './edit-debtor-dialog';
import { ViewDebtorDialog } from './view-debtor-dialog';

import type { Debtor } from '@/features/debtor/schemas';

interface DebtorManagementProps {
  debtors: Debtor[];
}

export function DebtorManagement({ debtors }: Readonly<DebtorManagementProps>) {
  const [searchTerm, setSearchTerm] = useState('');

  const [openCreateDebtorDialog, setOpenCreateDebtorDialog] = useState(false);
  const [openEditDebtorDialog, setOpenEditDebtorDialog] = useState(false);
  const [openViewDebtorDialog, setOpenViewDebtorDialog] = useState(false);
  const [openDeleteDebtorDialog, setOpenDeleteDebtorDialog] = useState(false);
  const [selectedDebtor, setSelectedDebtor] = useState<Debtor | null>(null);

  const handleViewDebtor = (debtor: Debtor) => {
    setSelectedDebtor(debtor);
    setOpenViewDebtorDialog(true);
  };

  const handleEditDebtor = (debtor: Debtor) => {
    setSelectedDebtor(debtor);
    setOpenEditDebtorDialog(true);
  };

  const handleDeleteDebtor = (debtor: Debtor) => {
    setSelectedDebtor(debtor);
    setOpenDeleteDebtorDialog(true);
  };

  const handleDebtorCreated = () => {
    setOpenCreateDebtorDialog(false);
  };

  const handleDebtorUpdated = () => {
    setOpenEditDebtorDialog(false);
    setSelectedDebtor(null);
  };

  const handleDebtorDeleted = () => {
    setOpenDeleteDebtorDialog(false);
    setSelectedDebtor(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'En proceso':
        return 'bg-blue-100 text-blue-800';
      case 'Audiencia programada':
        return 'bg-yellow-100 text-yellow-800';
      case 'Documentos pendientes':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredDebtors = debtors.filter((debtor) => {
    const matchesSearch =
      debtor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      debtor.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      debtor.idNumber.includes(searchTerm);
    return matchesSearch;
  });

  const exportToCSV = (data: Debtor[], filename: string) => {
    if (!data || data.length === 0) {
      toast.info('No hay datos para exportar.');
      return;
    }

    const spanishHeaders: Record<string, string> = {
      id: 'ID',
      name: 'Nombre',
      idNumber: 'Número de Identificación',
      idType: 'Tipo de Identificación',
      email: 'Correo Electrónico',
      phone: 'Teléfono',
      address: 'Dirección',
      city: 'Ciudad',
      department: 'Departamento',
      birthDate: 'Fecha de Nacimiento',
      maritalStatus: 'Estado Civil',
      occupation: 'Ocupación',
      monthlyIncome: 'Ingresos Mensuales',
      monthlyExpenses: 'Gastos Mensuales',
      dependents: 'Dependientes',
      educationLevel: 'Nivel Educativo',
      totalDebt: 'Deuda Total',
      status: 'Estado',
      emergencyContact: 'Contacto de Emergencia',
      emergencyPhone: 'Teléfono de Emergencia',
      bankAccount: 'Cuenta Bancaria',
      bankName: 'Nombre del Banco',
      accountType: 'Tipo de Cuenta',
      description: 'Descripción',
      activeCases: 'Casos Activos',
      createdDate: 'Fecha de Creación',
      lastUpdate: 'Última Actualización',
    };

    const csvRows = [];

    const headers = Object.keys(data[0]);
    const spanishHeaderRow = headers.map(
      (header) => spanishHeaders[header] || header,
    );
    csvRows.push(spanishHeaderRow.join(','));

    for (const row of data) {
      const values = headers.map((header) => {
        const cellValue = (row as unknown as Record<string, unknown>)[header];
        if (typeof cellValue === 'string') {
          return `"${cellValue.replace(/"/g, '""')}"`;
        }
        if (typeof cellValue === 'number' || typeof cellValue === 'boolean') {
          return cellValue.toString();
        }
        if (cellValue === null || cellValue === undefined) {
          return '';
        }
        return `"${JSON.stringify(cellValue).replace(/"/g, '""')}"`;
      });
      csvRows.push(values.join(','));
    }

    const csvData = csvRows.join('\n');
    const BOM = '\uFEFF';
    const blob = new Blob([BOM + csvData], { type: 'text/csv;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.setAttribute('href', url);
    a.setAttribute('download', filename);
    a.click();

    toast.success(`Los datos se han exportado a ${filename}`);
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Lista de Deudores</CardTitle>
              <CardDescription>
                Personas naturales en proceso de insolvencia
              </CardDescription>
            </div>
            <Button onClick={() => setOpenCreateDebtorDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Nuevo Deudor
            </Button>
          </div>
          <div className="mt-4 flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Buscar deudores..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-80 pl-10"
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => exportToCSV(filteredDebtors, 'deudores.csv')}
            >
              <Download className="mr-2 h-4 w-4" />
              Exportar
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Deudor</TableHead>
                  <TableHead>Cédula</TableHead>
                  <TableHead>Contacto</TableHead>
                  <TableHead>Ocupación</TableHead>
                  <TableHead>Ingresos</TableHead>
                  <TableHead>Deuda Total</TableHead>
                  <TableHead>Estado</TableHead>
                  <TableHead>Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredDebtors.map((debtor) => (
                  <TableRow key={debtor.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{debtor.name}</p>
                        <p className="flex items-center text-sm text-gray-600">
                          <MapPin className="mr-1 h-3 w-3" />
                          {debtor.address}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>{debtor.idNumber}</TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="flex items-center text-sm">
                          <Mail className="mr-1 h-3 w-3" />
                          {debtor.email}
                        </p>
                        <p className="flex items-center text-sm">
                          <Phone className="mr-1 h-3 w-3" />
                          {debtor.phone}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>{debtor.occupation}</TableCell>
                    <TableCell>
                      {formatCurrency(debtor.monthlyIncome)}
                    </TableCell>
                    <TableCell>{formatCurrency(debtor.totalDebt)}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(debtor.status)}>
                        {debtor.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewDebtor(debtor)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditDebtor(debtor)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteDebtor(debtor)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <CreateDebtorDialog
        open={openCreateDebtorDialog}
        onOpenChange={setOpenCreateDebtorDialog}
        onDebtorCreated={handleDebtorCreated}
      />

      {selectedDebtor && (
        <EditDebtorDialog
          debtor={selectedDebtor}
          open={openEditDebtorDialog}
          onOpenChange={setOpenEditDebtorDialog}
          onDebtorUpdated={handleDebtorUpdated}
        />
      )}

      {selectedDebtor && (
        <ViewDebtorDialog
          debtor={selectedDebtor}
          open={openViewDebtorDialog}
          onOpenChange={setOpenViewDebtorDialog}
        />
      )}

      {selectedDebtor && (
        <DeleteDebtorDialog
          debtor={selectedDebtor}
          open={openDeleteDebtorDialog}
          onOpenChange={setOpenDeleteDebtorDialog}
          onDebtorDeleted={handleDebtorDeleted}
        />
      )}
    </div>
  );
}
