'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useForm, Controller } from 'react-hook-form';
import { toast } from 'sonner';
import { useServerAction } from 'zsa-react';

import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { createRole } from '@/features/role/actions';
import {
  createRoleSchema,
  PERMISSIONS,
  getPermissionKeys,
} from '@/features/role/schemas';

interface CreateRoleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onRoleCreated?: () => void;
}

export function CreateRoleDialog({
  open,
  onOpenChange,
  onRoleCreated,
}: Readonly<CreateRoleDialogProps>) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
    control,
  } = useForm({
    resolver: zodResolver(createRoleSchema),
    defaultValues: {
      name: '',
      description: '',
      permissions: [],
      color: 'bg-gray-100 text-gray-800',
    },
  });

  const { execute, isPending } = useServerAction(createRole, {
    onSuccess: ({ data }) => {
      toast.success('Rol creado exitosamente', {
        description: `El rol ${data.name} ha sido creado correctamente`,
      });
      reset();
      onOpenChange(false);
      onRoleCreated?.();
    },
    onError: ({ err: { message } }) => {
      toast.error(message || 'Error al crear el rol');
    },
  });

  const availablePermissions = Object.values(PERMISSIONS).filter(
    (p) => p !== 'Acceso completo',
  );

  const colorOptions = [
    { value: 'bg-red-100 text-red-800', label: 'Rojo', preview: 'bg-red-100' },
    {
      value: 'bg-blue-100 text-blue-800',
      label: 'Azul',
      preview: 'bg-blue-100',
    },
    {
      value: 'bg-green-100 text-green-800',
      label: 'Verde',
      preview: 'bg-green-100',
    },
    {
      value: 'bg-yellow-100 text-yellow-800',
      label: 'Amarillo',
      preview: 'bg-yellow-100',
    },
    {
      value: 'bg-purple-100 text-purple-800',
      label: 'Morado',
      preview: 'bg-purple-100',
    },
    {
      value: 'bg-gray-100 text-gray-800',
      label: 'Gris',
      preview: 'bg-gray-100',
    },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle>Crear Nuevo Rol</DialogTitle>
          <DialogDescription>
            Configure un nuevo rol con permisos específicos
          </DialogDescription>
        </DialogHeader>

        <form
          onSubmit={handleSubmit((data) => {
            const permissionKeys = getPermissionKeys(data.permissions);
            execute({
              name: data.name,
              description: data.description,
              permissions: permissionKeys,
              color: data.color,
            });
          })}
          className="space-y-4"
        >
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name">Nombre del Rol *</Label>
              <Input
                id="name"
                placeholder="Ej: Coordinador Legal"
                {...register('name')}
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="color">Color del Badge</Label>
              <div className="flex space-x-2">
                {colorOptions.map((color) => (
                  <button
                    key={color.value}
                    type="button"
                    className={`h-8 w-8 rounded-full border-2 ${
                      color.preview
                    } ${
                      watch('color') === color.value
                        ? 'border-gray-800'
                        : 'border-gray-300'
                    }`}
                    onClick={() => setValue('color', color.value)}
                    title={color.label}
                  />
                ))}
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Descripción</Label>
            <Textarea
              id="description"
              placeholder="Descripción del rol y sus responsabilidades"
              {...register('description')}
            />
          </div>

          <div className="space-y-2">
            <Label>Permisos del Rol</Label>
            <div className="grid max-h-64 grid-cols-1 gap-2 overflow-y-auto rounded-lg border p-4 md:grid-cols-2">
              <Controller
                name="permissions"
                control={control}
                render={({ field }) => {
                  const handlePermissionChange = (
                    permission: string,
                    checked: boolean | 'indeterminate',
                  ) => {
                    const current = field.value || [];
                    if (checked === true) {
                      field.onChange([...current, permission]);
                    } else {
                      field.onChange(
                        current.filter((p: string) => p !== permission),
                      );
                    }
                  };

                  return (
                    <>
                      {availablePermissions.map((permission) => (
                        <div
                          key={permission}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={permission}
                            checked={field.value?.includes(permission)}
                            onCheckedChange={(checked) =>
                              handlePermissionChange(permission, checked)
                            }
                          />
                          <Label
                            htmlFor={permission}
                            className="cursor-pointer text-sm"
                          >
                            {permission}
                          </Label>
                        </div>
                      ))}
                    </>
                  );
                }}
              />
            </div>
            {errors.permissions && (
              <p className="text-sm text-red-500">
                {errors.permissions.message}
              </p>
            )}
            <p className="text-sm text-gray-500">
              Seleccionados: {watch('permissions')?.length || 0} permisos
            </p>
          </div>

          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outline" disabled={isPending}>
                Cancelar
              </Button>
            </DialogClose>
            <Button type="submit" disabled={isPending}>
              {isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creando...
                </>
              ) : (
                'Crear Rol'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
