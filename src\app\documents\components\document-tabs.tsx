'use client';

import { useState, useEffect } from 'react';
import { useServerAction } from 'zsa-react';

import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  type Document,
  type DocumentFromDrive,
} from '@/features/document/schemas';
import { DocumentTemplate } from '@/features/document/types';
import {
  getDocuments,
  getTemplatesFromDatabase,
  getDocumentsFromDrive,
} from '@/features/document/actions';

import { DocumentDetailsDialog } from './document-details-dialog';
import { DocumentFileTree } from './document-file-tree';
import { DocumentTemplateLibrary } from './document-template-library';
import { DocumentViewerDialog } from './document-viewer-dialog';

interface DocumentTabsProps {
  refreshTrigger: number;
}

export function DocumentTabs({ refreshTrigger }: Readonly<DocumentTabsProps>) {
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(
    null,
  );
  const [showViewerDialog, setShowViewerDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [documentsFromDrive, setDocumentsFromDrive] = useState<
    DocumentFromDrive[]
  >([]);
  const [templates, setTemplates] = useState<DocumentTemplate[]>([]);

  const { execute: loadDocuments } = useServerAction(getDocuments, {
    onSuccess: ({ data }) => {
      setDocuments(data);
    },
  });

  const { execute: loadTemplates } = useServerAction(getTemplatesFromDatabase, {
    onSuccess: ({ data }) => {
      setTemplates(data);
    },
  });

  const { execute: loadDocumentsFromDrive } = useServerAction(
    getDocumentsFromDrive,
    {
      onSuccess: ({ data }) => {
        setDocumentsFromDrive(data);
      },
    },
  );

  useEffect(() => {
    loadTemplates({});
    loadDocuments({});
    loadDocumentsFromDrive({});
  }, [loadTemplates, loadDocuments, loadDocumentsFromDrive, refreshTrigger]);

  const openDocumentViewer = (document: Document) => {
    setSelectedDocument(document);
    setShowViewerDialog(true);
  };

  const downloadDocument = (document: Document) => {
    setDocuments((docs) =>
      docs.map((doc) =>
        doc.id === document.id
          ? {
              ...doc,
              downloadCount: (doc.downloadCount ?? 0) + 1,
              lastAccessed: new Date().toISOString(),
            }
          : doc,
      ),
    );
    alert(`📥 Descargando: ${document.name}`);
  };

  const deleteDocument = (documentId: string) => {
    setDocuments((docs) => docs.filter((doc) => doc.id !== documentId));
    alert(`🗑️ Documento eliminado`);
  };

  const shareDocument = (document: Document) => {
    alert(`🔗 Compartiendo: ${document.name}`);
  };

  return (
    <>
      <Tabs defaultValue="documents" className="space-y-4">
        <TabsList>
          <TabsTrigger value="documents">Documentos</TabsTrigger>
          <TabsTrigger value="templates">Plantillas</TabsTrigger>
        </TabsList>

        <TabsContent value="documents" className="space-y-4">
          <DocumentFileTree
            documents={documents}
            documentsFromDrive={documentsFromDrive}
            templates={templates}
            onViewDocument={openDocumentViewer}
            onDownloadDocument={downloadDocument}
          />
        </TabsContent>

        <TabsContent value="templates">
          <DocumentTemplateLibrary
            templates={templates}
            refreshTrigger={refreshTrigger}
          />
        </TabsContent>
      </Tabs>

      {selectedDocument && (
        <>
          <DocumentViewerDialog
            open={showViewerDialog}
            onOpenChange={setShowViewerDialog}
            document={selectedDocument}
          />
          <DocumentDetailsDialog
            open={showDetailsDialog}
            onOpenChange={setShowDetailsDialog}
            document={selectedDocument}
            onDownload={downloadDocument}
            onShare={shareDocument}
            onDelete={deleteDocument}
          />
        </>
      )}
    </>
  );
}
