'use server';

import { revalidatePath } from 'next/cache';
import { createServerAction } from 'zsa';

import prisma from '@/lib/prisma';

import {
  createCreditorSchema,
  updateCreditorSchema,
  creditorWithCasesSchema,
  creditorSummarySchema,
  getAllCreditorsSchema,
  getCreditorByIdSchema,
  deleteCreditorSchema,
  toggleCreditorStatusSchema,
} from './schemas';

export const getAllCreditors = createServerAction()
  .output(getAllCreditorsSchema)
  .handler(async () => {
    return prisma.creditor.findMany({
      include: {
        contacts: true,
        debts: true,
        _count: {
          select: {
            debts: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });
  });

export const getCreditorById = createServerAction()
  .input(getCreditorByIdSchema)
  .output(creditorWithCasesSchema)
  .handler(async ({ input: id }) => {
    const creditor = await prisma.creditor.findUnique({
      where: { id },
      include: {
        contacts: true,
        debts: {
          include: {
            case: {
              include: {
                debtor: true,
              },
            },
          },
        },
        _count: {
          select: {
            debts: true,
          },
        },
      },
    });

    if (!creditor) {
      throw new Error('Acreedor no encontrado');
    }

    return creditor;
  });

export const createCreditor = createServerAction()
  .input(createCreditorSchema)
  .output(creditorSummarySchema)
  .handler(async ({ input }) => {
    const creditor = await prisma.creditor.create({
      data: input,
    });

    revalidatePath('/creditors');

    return creditor;
  });

export const updateCreditor = createServerAction()
  .input(updateCreditorSchema)
  .output(creditorSummarySchema)
  .handler(async ({ input: { id, ...rest } }) => {
    const updatedCreditor = await prisma.creditor.update({
      where: { id },
      data: rest,
    });

    revalidatePath('/creditors');

    return updatedCreditor;
  });

export const deleteCreditor = createServerAction()
  .input(deleteCreditorSchema)
  .output(creditorSummarySchema)
  .handler(async ({ input: { id } }) => {
    const deletedCreditor = await prisma.creditor.delete({
      where: { id },
    });

    revalidatePath('/creditors');

    return deletedCreditor;
  });

export const toggleCreditorStatus = createServerAction()
  .input(toggleCreditorStatusSchema)
  .output(creditorSummarySchema)
  .handler(async ({ input: { id } }) => {
    const creditor = await prisma.creditor.findUnique({ where: { id } });
    if (!creditor) {
      throw new Error('Acreedor no encontrado');
    }

    const newStatus = creditor.status === 'Activo' ? 'Inactivo' : 'Activo';

    const updatedCreditor = await prisma.creditor.update({
      where: { id },
      data: { status: newStatus },
    });

    revalidatePath('/creditors');

    return updatedCreditor;
  });
