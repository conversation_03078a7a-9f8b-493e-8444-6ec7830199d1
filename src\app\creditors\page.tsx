import { getAllCreditors } from '@/features/creditor/actions';

import { DashboardHeader } from '../dashboard/components/dashboard-header';
import { CreditorManagement } from './components/creditor-management';

export default async function CreditorsPage() {
  const [creditors] = await getAllCreditors();

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader />

      <main className="container mx-auto px-4 py-6">
        <CreditorManagement creditors={creditors || []} />
      </main>
    </div>
  );
}
