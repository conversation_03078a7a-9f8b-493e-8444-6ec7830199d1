import { DashboardHeader } from './components/dashboard-header';
import { DashboardStats } from './components/dashboard-stats';
import { NotificationPanel } from './components/notification-panel';
import { RecentCases } from './components/recent-cases';
import { TaskSelector } from './components/task-selector';

export default function DashboardPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader />

      <main className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
          <div className="space-y-6 lg:col-span-3">
            <DashboardStats />
            <TaskSelector />
            <RecentCases />
          </div>

          <div className="lg:col-span-1">
            <NotificationPanel />
          </div>
        </div>
      </main>
    </div>
  );
}
