'use client';

import { LucideIcon } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface TaskProps {
  id: string;
  title: string;
  description: string;
  icon: LucideIcon;
  color: string;
  iconColor: string;
  count: number;
  selected: boolean;
  onClick: () => void;
}

export function Task({
  title,
  description,
  icon: Icon,
  color,
  iconColor,
  count,
  selected,
  onClick,
}: Readonly<Omit<TaskProps, 'id'>>) {
  return (
    <Card
      onClick={onClick}
      className={cn(
        'hover:bg-accent/50 min-h-[120px] cursor-pointer gap-3 overflow-hidden p-4 text-left',
        selected ? 'bg-accent' : 'bg-card',
      )}
    >
      <CardHeader className="flex w-full flex-row items-center justify-between p-0">
        <div className={`rounded-lg p-2 ${color} shrink-0`}>
          <Icon className={`h-4 w-4 ${iconColor}`} />
        </div>
        {count > 0 && (
          <Badge variant="secondary" className="shrink-0 text-xs">
            {count}
          </Badge>
        )}
      </CardHeader>
      <CardContent className="w-full overflow-hidden p-0">
        <h3 className="mb-1 truncate text-sm leading-tight font-semibold">
          {title}
        </h3>
        <p className="text-xs leading-tight break-words hyphens-auto text-gray-500">
          <span className="line-clamp-2 block">{description}</span>
        </p>
      </CardContent>
    </Card>
  );
}
