'use server';

import { revalidateTag } from 'next/cache';
import { createServerAction } from 'zsa';

import prisma from '@/lib/prisma';

import {
  createCaseSchema,
  updateCaseSchema,
  addDebtToCaseSchema,
  removeDebtFromCaseSchema,
  caseWithRelationsSchema,
  caseSummarySchema,
  debtResponseSchema,
  getAllCasesSchema,
  getCaseByIdSchema,
  deleteCaseSchema,
  caseStatsSchema,
} from './schemas';

export const getAllCases = createServerAction()
  .output(getAllCasesSchema)
  .handler(async () => {
    const cases = await prisma.case.findMany({
      include: {
        debtor: true,
        operator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            documents: true,
          },
        },
      },
      orderBy: {
        createdDate: 'desc',
      },
    });

    return cases;
  });

export const getCaseById = createServerAction()
  .input(getCaseByIdSchema)
  .output(caseWithRelationsSchema)
  .handler(async ({ input: id }) => {
    const caseItem = await prisma.case.findUnique({
      where: { id },
      include: {
        debtor: true,
        operator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        documents: true,
        debts: {
          include: {
            creditor: {
              select: {
                name: true,
              },
            },
          },
        },
        assets: true,
        _count: {
          select: {
            documents: true,
          },
        },
      },
    });

    if (!caseItem) {
      throw new Error('Caso no encontrado');
    }

    return caseItem;
  });

export const createCase = createServerAction()
  .input(createCaseSchema)
  .output(caseSummarySchema)
  .handler(
    async ({
      input: {
        debtorId,
        hearingDate,
        caseNumber,
        filingDate,
        designationDate,
        positionAcceptanceDate,
        inadmissionDate,
        admissionDate,
        firstHearingDate,
        suspensionDate,
        resultDeliveryDate,
        resultDate,
        ...rest
      },
    }) => {
      let finalCaseNumber = caseNumber;
      if (!finalCaseNumber) {
        const lastCase = await prisma.case.findFirst({
          orderBy: { createdDate: 'desc' },
          select: { caseNumber: true },
        });

        const lastNumber = lastCase?.caseNumber
          ? parseInt(lastCase.caseNumber.split('-')[1]) || 0
          : 0;
        finalCaseNumber = `CASO-${(lastNumber + 1).toString().padStart(6, '0')}`;
      }

      const debtor = await prisma.debtor.findUnique({
        where: { id: debtorId },
        select: { name: true },
      });

      if (!debtor) {
        throw new Error('Deudor no encontrado');
      }

      const newCase = await prisma.case.create({
        data: {
          ...rest,
          debtorId,
          caseNumber: finalCaseNumber,
          debtorName: debtor.name,
          status: rest.status ?? 'NEGOTIATION',
          phase: rest.phase ?? 'Inicial',
          hearingDate:
            hearingDate && hearingDate.trim() ? new Date(hearingDate) : null,
          filingDate:
            filingDate && filingDate.trim() ? new Date(filingDate) : null,
          designationDate:
            designationDate && designationDate.trim()
              ? new Date(designationDate)
              : null,
          positionAcceptanceDate:
            positionAcceptanceDate && positionAcceptanceDate.trim()
              ? new Date(positionAcceptanceDate)
              : null,
          inadmissionDate:
            inadmissionDate && inadmissionDate.trim()
              ? new Date(inadmissionDate)
              : null,
          admissionDate:
            admissionDate && admissionDate.trim()
              ? new Date(admissionDate)
              : null,
          firstHearingDate:
            firstHearingDate && firstHearingDate.trim()
              ? new Date(firstHearingDate)
              : null,
          suspensionDate:
            suspensionDate && suspensionDate.trim()
              ? new Date(suspensionDate)
              : null,
          resultDeliveryDate:
            resultDeliveryDate && resultDeliveryDate.trim()
              ? new Date(resultDeliveryDate)
              : null,
          resultDate:
            resultDate && resultDate.trim() ? new Date(resultDate) : null,
        },
      });

      revalidateTag('cases');

      return newCase;
    },
  );

export const updateCase = createServerAction()
  .input(updateCaseSchema)
  .output(caseSummarySchema)
  .handler(
    async ({
      input: {
        id,
        hearingDate,
        filingDate,
        designationDate,
        positionAcceptanceDate,
        inadmissionDate,
        admissionDate,
        firstHearingDate,
        suspensionDate,
        resultDeliveryDate,
        resultDate,
        ...rest
      },
    }) => {
      const updatedCase = await prisma.case.update({
        where: { id },
        data: {
          ...rest,
          hearingDate:
            hearingDate && hearingDate.trim()
              ? new Date(hearingDate)
              : undefined,
          filingDate:
            filingDate && filingDate.trim() ? new Date(filingDate) : undefined,
          designationDate:
            designationDate && designationDate.trim()
              ? new Date(designationDate)
              : undefined,
          positionAcceptanceDate:
            positionAcceptanceDate && positionAcceptanceDate.trim()
              ? new Date(positionAcceptanceDate)
              : undefined,
          inadmissionDate:
            inadmissionDate && inadmissionDate.trim()
              ? new Date(inadmissionDate)
              : undefined,
          admissionDate:
            admissionDate && admissionDate.trim()
              ? new Date(admissionDate)
              : undefined,
          firstHearingDate:
            firstHearingDate && firstHearingDate.trim()
              ? new Date(firstHearingDate)
              : undefined,
          suspensionDate:
            suspensionDate && suspensionDate.trim()
              ? new Date(suspensionDate)
              : undefined,
          resultDeliveryDate:
            resultDeliveryDate && resultDeliveryDate.trim()
              ? new Date(resultDeliveryDate)
              : undefined,
          resultDate:
            resultDate && resultDate.trim() ? new Date(resultDate) : undefined,
        },
      });

      revalidateTag('cases');

      return updatedCase;
    },
  );

export const addDebtToCase = createServerAction()
  .input(addDebtToCaseSchema)
  .output(debtResponseSchema)
  .handler(
    async ({ input: { caseId, creditorId, amount, type, interestRate } }) => {
      const caseData = await prisma.case.findUnique({
        where: { id: caseId },
        select: { debtorId: true },
      });

      if (!caseData) {
        throw new Error('Caso no encontrado');
      }

      const debt = await prisma.debt.create({
        data: {
          amount: parseFloat(amount.replace(/[^0-9.-]/g, '')),
          interestRate: parseFloat(interestRate),
          type,
          caseId,
          creditorId,
          debtorId: caseData.debtorId,
        },
        include: {
          creditor: {
            select: {
              name: true,
            },
          },
        },
      });

      revalidateTag('cases');

      return {
        id: debt.id,
        amount: debt.amount,
        interestRate: debt.interestRate,
        type: debt.type,
        creditor: debt.creditor.name,
        creditorId: debt.creditorId,
      };
    },
  );

export const removeDebtFromCase = createServerAction()
  .input(removeDebtFromCaseSchema)
  .handler(async ({ input: { debtId } }) => {
    const deletedDebt = await prisma.debt.delete({
      where: { id: debtId },
      include: {
        creditor: true,
      },
    });

    revalidateTag('cases');

    return {
      id: deletedDebt.id,
      creditor: deletedDebt.creditor.name,
      creditorId: deletedDebt.creditorId,
      amount: deletedDebt.amount.toString(),
      type: deletedDebt.type,
      interestRate: deletedDebt.interestRate.toString(),
    };
  });

export const deleteCase = createServerAction()
  .input(deleteCaseSchema)
  .output(caseSummarySchema)
  .handler(async ({ input: { id } }) => {
    const deletedCase = await prisma.case.delete({
      where: { id },
    });

    revalidateTag('cases');

    return deletedCase;
  });

export const getCaseStats = createServerAction()
  .output(caseStatsSchema)
  .handler(async () => {
    const [total, byStatus, byType, totalDebtResult] = await Promise.all([
      prisma.case.count(),
      prisma.case.groupBy({
        by: ['status'],
        _count: {
          id: true,
        },
      }),
      prisma.case.groupBy({
        by: ['type'],
        _count: {
          id: true,
        },
      }),
      prisma.case.aggregate({
        _sum: {
          totalDebt: true,
        },
      }),
    ]);

    const negotiation = await prisma.case.count({
      where: { status: 'NEGOTIATION' },
    });

    const agreementApproved = await prisma.case.count({
      where: { status: 'AGREEMENT_APPROVED' },
    });

    return {
      total,
      byStatus,
      byType,
      negotiation,
      agreementApproved,
      totalDebt: totalDebtResult._sum.totalDebt,
    };
  });
