'use client';

import { Calendar, Users } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';

import type { Case } from '@/features/case/schemas';

interface CaseCalendarProps {
  cases: Case[];
  onCaseClick: (caseItem: Case) => void;
}

export function CaseCalendar({
  cases,
  onCaseClick,
}: Readonly<CaseCalendarProps>) {
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Insolvencia':
        return 'bg-red-100 text-red-800';
      case 'Conciliación':
        return 'bg-blue-100 text-blue-800';
      case 'Acuerdo de Apoyo':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const casesWithHearings = cases.filter((c) => c.hearingDate);

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {casesWithHearings.map((caseItem) => (
        <Card
          key={caseItem.id}
          className="cursor-pointer border-l-4 border-l-blue-500 hover:shadow-md"
          onClick={() => onCaseClick(caseItem)}
        >
          <CardContent className="p-4">
            <div className="mb-2 flex items-center justify-between">
              <Badge className={getTypeColor(caseItem.type)}>
                {caseItem.type}
              </Badge>
              <span className="text-sm text-gray-500">
                {caseItem.caseNumber}
              </span>
            </div>
            <h3 className="font-medium">{caseItem.debtorName}</h3>
            <div className="mt-2 flex items-center text-sm text-gray-600">
              <Calendar className="mr-1 h-4 w-4" />
              {new Date(caseItem.hearingDate!).toLocaleDateString('es-CO')}
            </div>
            <div className="mt-1 flex items-center text-sm text-gray-600">
              <Users className="mr-1 h-4 w-4" />
              {caseItem.creditors} acreedores
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
