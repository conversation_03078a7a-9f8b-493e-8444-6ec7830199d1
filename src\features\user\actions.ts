'use server';

import { revalidateTag } from 'next/cache';
import { createServerAction } from 'zsa';
import bcrypt from 'bcrypt';
import { z } from 'zod';

import prisma from '@/lib/prisma';

import {
  createUserSchema,
  updateUserSchema,
  userSchema,
  userSummarySchema,
  resetUserPasswordResultSchema,
} from './schemas';

export const getAllUsers = createServerAction()
  .output(z.array(userSchema))
  .handler(async () => {
    return prisma.user.findMany({
      include: {
        role: true,
        assignedCases: { select: { id: true } },
      },
      orderBy: {
        name: 'asc',
      },
    });
  });

export const createUser = createServerAction()
  .input(createUserSchema)
  .output(userSummarySchema)
  .handler(async ({ input: { roleId, ...rest } }) => {
    const createdUser = await prisma.user.create({
      data: { ...rest, role: { connect: { id: roleId } } },
    });

    revalidateTag('users');

    return createdUser;
  });

export const updateUser = createServerAction()
  .input(updateUserSchema)
  .output(userSummarySchema)
  .handler(async ({ input: { id, roleId, ...rest } }) => {
    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        ...rest,
        ...(roleId && { role: { connect: { id: roleId } } }),
      },
    });

    revalidateTag('users');

    return updatedUser;
  });

export const deleteUser = createServerAction()
  .input(z.string())
  .output(userSummarySchema)
  .handler(async ({ input: id }) => {
    const deletedUser = await prisma.user.delete({ where: { id } });

    revalidateTag('users');

    return deletedUser;
  });

export const toggleUserStatus = createServerAction()
  .input(z.string())
  .output(userSummarySchema)
  .handler(async ({ input: id }) => {
    const user = await prisma.user.findUnique({ where: { id } });
    if (!user) {
      throw new Error('Usuario no encontrado');
    }

    const newStatus = user.status === 'Activo' ? 'Inactivo' : 'Activo';

    const updatedUser = await prisma.user.update({
      where: { id },
      data: { status: newStatus },
    });

    revalidateTag('users');

    return updatedUser;
  });

export const resetUserPassword = createServerAction()
  .input(z.string())
  .output(resetUserPasswordResultSchema)
  .handler(async ({ input: id }) => {
    const user = await prisma.user.findUnique({ where: { id } });
    if (!user) {
      throw new Error('Usuario no encontrado');
    }

    const randomPassword = Math.random().toString(36).slice(-8);
    const hashedPassword = await bcrypt.hash(randomPassword, 12);

    const updatedUser = await prisma.user.update({
      where: { id },
      data: { password: hashedPassword },
    });

    revalidateTag('users');

    return { ...updatedUser, tempPassword: randomPassword };
  });
