'use client';

import {
  FileText,
  Download,
  Calendar,
  Users,
  DollarSign,
  Clock,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { AmortizationCalculator } from './amortization-calculator';
import { DocumentGenerator } from './document-generator';

import type { CaseWithRelations } from '@/features/case/schemas';

interface CaseDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  case: CaseWithRelations;
  loading?: boolean;
}

export function CaseDetailsDialog({
  open,
  onOpenChange,
  case: caseData,
  loading = false,
}: Readonly<CaseDetailsDialogProps>) {
  if (!caseData) return null;

  if (loading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-4xl">
          <DialogHeader>
            <DialogTitle>Cargando detalles del caso...</DialogTitle>
          </DialogHeader>
          <div className="flex items-center justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"></div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  const creditors =
    caseData.debts?.map((debt) => ({
      name: debt.creditor?.name || 'Acreedor no especificado',
      amount: Number(debt.amount),
      type: debt.type || 'No especificado',
      status: 'Notificado',
    })) || [];

  const timeline = [
    {
      id: '1',
      date: '2025-01-15',
      event: 'Solicitud radicada',
      status: 'completed',
    },
    {
      id: '2',
      date: '2025-01-16',
      event: 'Control de legalidad',
      status: 'completed',
    },
    {
      id: '3',
      date: '2025-01-17',
      event: 'Auto de admisión',
      status: 'completed',
    },
    { id: '4', date: '2025-01-17', event: 'Designación', status: 'completed' },
    { id: '5', date: '2025-01-17', event: 'Aceptación', status: 'completed' },
    {
      id: '6',
      date: '2025-01-18',
      event: 'Notificación a acreedores',
      status: 'completed',
    },
    {
      id: '7',
      date: '2025-02-15',
      event: 'Audiencia de negociación',
      status: 'pending',
    },
    { id: '8', date: 'TBD', event: 'Acuerdo de pago', status: 'future' },
    { id: '9', date: 'TBD', event: 'Inadmite (términos)', status: 'future' },
  ];

  const getTimelineStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500';
      case 'pending':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-400';
    }
  };

  const getTimelineStatusLabel = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completado';
      case 'pending':
        return 'Pendiente';
      case 'future':
        return 'Futuro';
      default:
        return '';
    }
  };

  const getTimelineBadgeColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-orange-100 text-orange-800';
      case 'future':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTimelineTextColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-800';
      case 'pending':
        return 'text-yellow-800';
      default:
        return 'text-gray-800';
    }
  };

  const getTimelineBorderColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'border-green-200';
      case 'pending':
        return 'border-yellow-200';
      default:
        return 'border-gray-200';
    }
  };

  const getTimelineBackgroundColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-50';
      case 'pending':
        return 'bg-yellow-50';
      default:
        return 'bg-gray-50';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-4xl">
        <DialogHeader>
          <DialogTitle>Detalles del Caso {caseData?.id || 'N/A'}</DialogTitle>
          <DialogDescription>
            Información completa del caso de {caseData?.debtorName || 'N/A'}
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full grid-cols-8">
            <TabsTrigger value="overview">Resumen</TabsTrigger>
            <TabsTrigger value="case-info">Info Caso</TabsTrigger>
            <TabsTrigger value="legal-info">Info Legal</TabsTrigger>
            <TabsTrigger value="creditors">Acreedores</TabsTrigger>
            <TabsTrigger value="documents">Documentos</TabsTrigger>
            <TabsTrigger value="timeline">Cronología</TabsTrigger>
            <TabsTrigger value="calculator">Calculadora</TabsTrigger>
            <TabsTrigger value="generator">Generar</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Monto Total
                      </p>
                      <p className="text-2xl font-bold">
                        ${(caseData?.totalDebt || 0).toLocaleString()}
                      </p>
                    </div>
                    <DollarSign className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Acreedores
                      </p>
                      <p className="text-2xl font-bold">{caseData.creditors}</p>
                    </div>
                    <Users className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Días Activo
                      </p>
                      <p className="text-2xl font-bold">
                        {Math.floor(
                          (new Date().getTime() -
                            new Date(caseData.createdDate).getTime()) /
                            (1000 * 60 * 60 * 24),
                        )}
                      </p>
                    </div>
                    <Clock className="h-8 w-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Documentos
                      </p>
                      <p className="text-2xl font-bold">
                        {caseData.documents?.length ??
                          caseData._count.documents}
                      </p>
                    </div>
                    <FileText className="h-8 w-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Información del Deudor</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Nombre Completo
                    </p>
                    <p className="text-lg">{caseData.debtorName}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Cédula</p>
                    <p className="text-lg">{caseData.debtor.idNumber}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Tipo de Caso
                    </p>
                    <Badge className="mt-1">{caseData.type}</Badge>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Estado Actual
                    </p>
                    <Badge className="mt-1">{caseData.status}</Badge>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Operador Asignado
                    </p>
                    <p className="text-lg">{caseData.operator.name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Próxima Audiencia
                    </p>
                    <p className="text-lg">
                      {caseData.hearingDate
                        ? new Date(caseData.hearingDate).toLocaleDateString(
                            'es-CO',
                          )
                        : 'No programada'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {caseData.causes && caseData.causes.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Causas de la Insolvencia</CardTitle>
                  <CardDescription>
                    Factores que contribuyeron a la situación de insolvencia
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
                    {caseData.causes.map((cause) => (
                      <div key={cause} className="flex items-center space-x-2">
                        <div className="h-2 w-2 rounded-full bg-blue-600"></div>
                        <span className="text-sm">{cause}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="case-info" className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Información Básica del Caso</CardTitle>
                  <CardDescription>
                    Datos fundamentales del trámite
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Trámite
                      </p>
                      <p className="text-lg">
                        {caseData.tramite || 'No especificado'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Fecha de Radicación
                      </p>
                      <p className="text-lg">
                        {caseData.filingDate
                          ? new Date(caseData.filingDate).toLocaleDateString(
                              'es-CO',
                            )
                          : 'No especificada'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Cédula del Deudor
                      </p>
                      <p className="text-lg">
                        {caseData.debtorIdNumber || 'No especificada'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Apoderado
                      </p>
                      <p className="text-lg">
                        {caseData.attorney || 'No asignado'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Capital Adeudado
                      </p>
                      <p className="text-lg">
                        {caseData.owedCapital
                          ? `$${Number(caseData.owedCapital).toLocaleString()}`
                          : 'No especificado'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Operador Designado
                      </p>
                      <p className="text-lg">
                        {caseData.designatedOperator || 'No asignado'}
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {caseData.convened && (
                      <Badge className="bg-blue-100 text-blue-800">
                        Convocado: {caseData.convened}
                      </Badge>
                    )}
                    {caseData.rejection && (
                      <Badge className="bg-red-100 text-red-800">
                        Rechazado
                      </Badge>
                    )}
                    {caseData.withdrawal && (
                      <Badge className="bg-yellow-100 text-yellow-800">
                        Desiste
                      </Badge>
                    )}
                    {caseData.hasLegalProcesses && (
                      <Badge className="bg-orange-100 text-orange-800">
                        Procesos en Contra
                      </Badge>
                    )}
                    {caseData.riskCenterCommunication && (
                      <Badge className="bg-purple-100 text-purple-800">
                        Comunicado a Central de Riesgo
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Fechas Importantes</CardTitle>
                  <CardDescription>Cronología del proceso</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Fecha de Designación
                      </p>
                      <p className="text-lg">
                        {caseData.designationDate
                          ? new Date(
                              caseData.designationDate,
                            ).toLocaleDateString('es-CO')
                          : 'No especificada'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Fecha de Aceptación del Cargo
                      </p>
                      <p className="text-lg">
                        {caseData.positionAcceptanceDate
                          ? new Date(
                              caseData.positionAcceptanceDate,
                            ).toLocaleDateString('es-CO')
                          : 'No especificada'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Fecha de Inadmisión
                      </p>
                      <p className="text-lg">
                        {caseData.inadmissionDate
                          ? new Date(
                              caseData.inadmissionDate,
                            ).toLocaleDateString('es-CO')
                          : 'No especificada'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Fecha de Admisión
                      </p>
                      <p className="text-lg">
                        {caseData.admissionDate
                          ? new Date(caseData.admissionDate).toLocaleDateString(
                              'es-CO',
                            )
                          : 'No especificada'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Primera Audiencia
                      </p>
                      <p className="text-lg">
                        {caseData.firstHearingDate
                          ? `${new Date(caseData.firstHearingDate).toLocaleDateString('es-CO')} ${caseData.firstHearingTime ? `a las ${caseData.firstHearingTime}` : ''}`
                          : 'No programada'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="legal-info" className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Información del Proceso Legal</CardTitle>
                  <CardDescription>
                    Detalles sobre procesos judiciales
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Número de Juzgado
                      </p>
                      <p className="text-lg">
                        {caseData.courtNumber || 'No especificado'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Ciudad
                      </p>
                      <p className="text-lg">
                        {caseData.city || 'No especificada'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Tipo de Proceso
                      </p>
                      <p className="text-lg">
                        {caseData.processType || 'No especificado'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Demandante
                      </p>
                      <p className="text-lg">
                        {caseData.plaintiff || 'No especificado'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        No. Radicado Judicial
                      </p>
                      <p className="text-lg">
                        {caseData.judicialFileNumber || 'No especificado'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Fecha de Suspensión
                      </p>
                      <p className="text-lg">
                        {caseData.suspensionDate
                          ? new Date(
                              caseData.suspensionDate,
                            ).toLocaleDateString('es-CO')
                          : 'No especificada'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Información del Resultado</CardTitle>
                  <CardDescription>
                    Detalles sobre el resultado del proceso
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Tipo de Resultado
                      </p>
                      <p className="text-lg">
                        {caseData.resultType ? (
                          <Badge
                            className={
                              caseData.resultType === 'ACUERDO'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }
                          >
                            {caseData.resultType === 'ACUERDO'
                              ? 'Acuerdo'
                              : 'Fracaso'}
                          </Badge>
                        ) : (
                          'No especificado'
                        )}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Fecha de Resultado
                      </p>
                      <p className="text-lg">
                        {caseData.resultDate
                          ? new Date(caseData.resultDate).toLocaleDateString(
                              'es-CO',
                            )
                          : 'No especificada'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Fecha de Entrega de Resultado
                      </p>
                      <p className="text-lg">
                        {caseData.resultDeliveryDate
                          ? new Date(
                              caseData.resultDeliveryDate,
                            ).toLocaleDateString('es-CO')
                          : 'No especificada'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        Número de SICCAC
                      </p>
                      <p className="text-lg">
                        {caseData.siccacNumber || 'No especificado'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="creditors" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Lista de Acreedores</CardTitle>
                <CardDescription>
                  Acreedores registrados en el caso
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Acreedor</TableHead>
                      <TableHead>Monto</TableHead>
                      <TableHead>Clase</TableHead>
                      <TableHead>Estado</TableHead>
                      <TableHead>Acciones</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {creditors.map((creditor) => (
                      <TableRow key={creditor.name}>
                        <TableCell className="font-medium">
                          {creditor.name}
                        </TableCell>
                        <TableCell>
                          ${creditor.amount.toLocaleString()}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{creditor.type}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            className={
                              creditor.status === 'Notificado'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-orange-100 text-orange-800'
                            }
                          >
                            {creditor.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button variant="ghost" size="sm">
                            <FileText className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Documentos del Caso</CardTitle>
                <CardDescription>
                  Todos los documentos asociados al caso de insolvencia
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  {(caseData.documents ?? []).map((doc, index) => {
                    const label = typeof doc === 'string' ? doc : doc.name;
                    const key = typeof doc === 'string' ? index : doc.id;
                    return (
                      <div
                        key={key}
                        className="flex items-center justify-between rounded-lg border p-3"
                      >
                        <div className="flex items-center space-x-3">
                          <FileText className="h-5 w-5 text-blue-600" />
                          <span>{label}</span>
                        </div>
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="timeline" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Cronología del Caso</CardTitle>
                <CardDescription>
                  Historial de eventos y próximos pasos
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="relative mt-4">
                  <div
                    className="absolute top-0 left-4 h-full w-0.5 bg-gray-200"
                    aria-hidden="true"
                  />
                  {timeline.map((item) => (
                    <div key={item.id} className="relative mb-8 pl-8">
                      <div
                        className={`absolute top-1.5 left-2.5 h-3 w-3 rounded-full ${getTimelineStatusColor(
                          item.status,
                        )}`}
                      />
                      <div
                        className={`flex items-center justify-between rounded-lg border p-4 ${getTimelineBorderColor(
                          item.status,
                        )} ${getTimelineBackgroundColor(item.status)}`}
                      >
                        <div>
                          <p
                            className={`font-semibold ${getTimelineTextColor(
                              item.status,
                            )}`}
                          >
                            {item.event}
                          </p>
                          <p className="text-sm text-gray-500">{item.date}</p>
                        </div>
                        <Badge className={getTimelineBadgeColor(item.status)}>
                          {getTimelineStatusLabel(item.status)}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="calculator">
            <AmortizationCalculator />
          </TabsContent>

          <TabsContent value="generator">
            <DocumentGenerator />
          </TabsContent>
        </Tabs>

        <div className="flex justify-end space-x-2 pt-4">
          <DialogClose asChild>
            <Button variant="outline">Cerrar</Button>
          </DialogClose>
          <Button>
            <Calendar className="mr-2 h-4 w-4" />
            Programar Audiencia
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
