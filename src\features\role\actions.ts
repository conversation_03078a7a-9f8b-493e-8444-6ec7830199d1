'use server';

import { revalidateTag } from 'next/cache';
import { createServerAction } from 'zsa';
import { z } from 'zod';

import prisma from '@/lib/prisma';

import { createRoleSchema, updateRoleSchema, roleSchema } from './schemas';

export const getAllRoles = createServerAction()
  .output(z.array(roleSchema))
  .handler(async () => {
    return prisma.role.findMany({
      include: { users: true },
    });
  });

export const createRole = createServerAction()
  .input(createRoleSchema)
  .output(roleSchema)
  .handler(async ({ input: { ...data } }) => {
    const newRole = await prisma.role.create({
      data,
      include: {
        users: true,
      },
    });

    revalidateTag('roles');

    return newRole;
  });

export const updateRole = createServerAction()
  .input(updateRoleSchema)
  .output(roleSchema)
  .handler(async ({ input: { id, ...data } }) => {
    const updatedRole = await prisma.role.update({
      where: { id },
      data,
      include: {
        users: true,
      },
    });

    revalidateTag('roles');

    return updatedRole;
  });

export const deleteRole = createServerAction()
  .input(z.string())
  .output(roleSchema)
  .handler(async ({ input: id }) => {
    const usersWithRole = await prisma.user.count({
      where: { roleId: id },
    });

    if (usersWithRole > 0) {
      throw new Error(
        'No se puede eliminar el rol porque tiene usuarios asignados',
      );
    }

    const deletedRole = await prisma.role.delete({
      where: { id },
      include: {
        users: true,
      },
    });

    revalidateTag('roles');

    return deletedRole;
  });

export const updateRolePermissions = createServerAction()
  .input(
    z.object({
      roleId: z.string(),
      permissions: z.array(z.string()),
    }),
  )
  .output(roleSchema)
  .handler(async ({ input: { roleId, permissions } }) => {
    const updatedRole = await prisma.role.update({
      where: { id: roleId },
      data: { permissions },
      include: {
        users: true,
      },
    });

    revalidateTag('roles');

    return updatedRole;
  });
