'use client';

import { Cloud, Loader2, Info } from 'lucide-react';
import { useServerAction } from 'zsa-react';

import { Button } from '@/components/ui/button';
import {
  syncTemplatesWithGoogleDrive,
  syncDocumentsWithGoogleDrive,
} from '@/features/document/actions';

interface DocumentsHeaderProps {
  onShowPlaceholders: () => void;
  onTemplatesSynced: () => void;
  onDocumentsSynced: () => void;
}

export function DocumentsHeader({
  onShowPlaceholders,
  onTemplatesSynced,
  onDocumentsSynced,
}: Readonly<DocumentsHeaderProps>) {
  const { execute: handleSyncTemplates, isPending: isSyncingTemplates } =
    useServerAction(syncTemplatesWithGoogleDrive, {
      onSuccess: () => {
        onTemplatesSynced();
      },
    });

  const { execute: handleSyncDocuments, isPending: isSyncingDocuments } =
    useServerAction(syncDocumentsWithGoogleDrive, {
      onSuccess: () => {
        onDocumentsSynced();
      },
    });

  const handleSyncAll = async () => {
    await handleSyncTemplates({});
    await handleSyncDocuments({});
  };

  const isSyncing = isSyncingTemplates || isSyncingDocuments;

  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">
          Gestión de Documentos
        </h1>
        <p className="text-gray-600">
          Administre documentos legales y sincronice plantillas desde Google
          Drive
        </p>
      </div>
      <div className="flex space-x-2">
        <Button variant="outline" onClick={handleSyncAll}>
          {(() => {
            if (isSyncing) {
              return (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sincronizando...
                </>
              );
            }
            return (
              <>
                <Cloud className="mr-2 h-4 w-4" />
                Sincronizar con Drive
              </>
            );
          })()}
        </Button>
        <Button onClick={onShowPlaceholders}>
          <Info className="mr-2 h-4 w-4" />
          Ver Placeholders
        </Button>
      </div>
    </div>
  );
}
