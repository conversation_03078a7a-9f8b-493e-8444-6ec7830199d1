'use client';

import { Upload } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

export function AssetsSection() {
  return (
    <Card className="shadow-sm">
      <CardHeader>
        <CardTitle>Bienes del Deudor</CardTitle>
        <CardDescription>Registre los bienes del deudor</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="py-8 text-center">
          <p className="text-gray-500">
            Funcionalidad de gestión de bienes en desarrollo
          </p>
          <Button variant="outline" className="mt-4">
            <Upload className="mr-2 h-4 w-4" />
            Subir Inventario
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
