'use client';

import { useState, useEffect } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { updateCase, getCaseById } from '@/features/case/actions';
import {
  updateCaseFormSchema,
  type UpdateCaseFormData,
  type Case,
  type DebtorOption,
  type CreditorOption,
} from '@/features/case/schemas';
import { useServerAction } from 'zsa-react';
import { getAllCreditors } from '@/features/creditor/actions';
import { getAllDebtors, createDebtor } from '@/features/debtor/actions';

import { AssetsSection } from './create-case-dialog/assets-section';
import { CreateDebtorDialog } from './create-case-dialog/create-debtor-dialog';
import { CreditorsTab } from './edit-case-dialog/creditors-tab';
import { DebtorSection } from './create-case-dialog/debtor-section';
import { DocumentsSection } from './create-case-dialog/documents-section';
import { CaseDetailsSection } from './create-case-dialog/case-details-section';
import { LegalProcessSection } from './create-case-dialog/legal-process-section';

interface EditCaseDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  case: Case;
  onCaseUpdated: () => void;
}

export function EditCaseDialog({
  open,
  onOpenChange,
  case: caseData,
  onCaseUpdated,
}: Readonly<EditCaseDialogProps>) {
  const [dataLoading, setDataLoading] = useState(false);
  const [debtors, setDebtors] = useState<DebtorOption[]>([]);
  const [creditors, setCreditors] = useState<CreditorOption[]>([]);
  const [selectedDebtorId, setSelectedDebtorId] = useState<string>('');
  const [selectedDebtor, setSelectedDebtor] = useState<DebtorOption | null>(
    null,
  );
  const [showCreateDebtorDialog, setShowCreateDebtorDialog] = useState(false);
  const [newDebtorForm, setNewDebtorForm] = useState({
    name: '',
    idNumber: '',
    email: '',
    phone: '',
    address: '',
    city: 'Bogotá',
    department: 'Cundinamarca',
    monthlyIncome: 0,
    monthlyExpenses: 0,
  });
  const [creatingDebtor, setCreatingDebtor] = useState(false);
  const [initializedCaseId, setInitializedCaseId] = useState<string | null>(
    null,
  );

  const form = useForm({
    resolver: zodResolver(updateCaseFormSchema),
    defaultValues: {
      id: caseData.id,
      debtorId: caseData.debtorId,
      type: caseData.type as
        | 'INSOLVENCY'
        | 'CONCILIATION'
        | 'SUPPORT_AGREEMENT',
      status: caseData.status,
      totalDebt: caseData.totalDebt.toString(),
      creditors: caseData.creditors,
      operatorId: caseData.operatorId,
      causes: caseData.causes ?? [],
      debts: [],
      assets: [],
      phase: caseData.phase ?? 'Inicial',
      hearingDate: caseData.hearingDate
        ? caseData.hearingDate.toISOString().split('T')[0]
        : null,
    },
  });

  const { execute: executeUpdateCase, isPending } = useServerAction(
    updateCase,
    {
      onSuccess: () => {
        toast.success('Caso actualizado exitosamente');
        onCaseUpdated();
        onOpenChange(false);
        form.reset();
      },
      onError: ({ err }) => {
        if (err.code === 'INPUT_PARSE_ERROR' && err.fieldErrors) {
          const firstError = Object.values(err.fieldErrors)[0]?.[0];
          toast.error(firstError ?? 'Error de validación en el formulario');
        } else {
          toast.error(err.message ?? 'Error al actualizar el caso');
        }
      },
    },
  );

  useEffect(() => {
    if (
      open &&
      caseData &&
      (initializedCaseId !== caseData.id || !initializedCaseId)
    ) {
      const loadData = async () => {
        setDataLoading(true);
        try {
          const [[debtorsData], [creditorsData], [fullCase]] =
            await Promise.all([
              getAllDebtors(),
              getAllCreditors(),
              getCaseById(caseData.id),
            ]);
          setDebtors(
            (debtorsData || []).map((debtor) => ({
              id: debtor.id,
              name: debtor.name,
              idNumber: debtor.idNumber,
              email: debtor.email,
              phone: debtor.phone,
              address: debtor.address,
              city: debtor.city,
              department: debtor.department,
              monthlyIncome: Number(debtor.monthlyIncome),
              monthlyExpenses: Number(debtor.monthlyExpenses ?? 0),
            })),
          );
          setCreditors(creditorsData as CreditorOption[]);

          if (fullCase) {
            setSelectedDebtorId(fullCase.debtorId);

            let caseTypeValue = '';
            if (fullCase.type === 'Insolvencia') caseTypeValue = 'INSOLVENCY';
            else if (fullCase.type === 'Conciliación')
              caseTypeValue = 'CONCILIATION';
            else if (fullCase.type === 'Acuerdo de Apoyo')
              caseTypeValue = 'SUPPORT_AGREEMENT';

            form.reset({
              id: fullCase.id,
              debtorId: fullCase.debtorId,
              type: caseTypeValue as
                | 'INSOLVENCY'
                | 'CONCILIATION'
                | 'SUPPORT_AGREEMENT',
              status: fullCase.status,
              totalDebt: fullCase.totalDebt.toString(),
              creditors: fullCase.creditors,
              operatorId: fullCase.operatorId,
              causes: fullCase.causes ?? [],
              debts: (fullCase.debts ?? []).map((debt) => ({
                id: debt.id,
                creditor: debt.creditor.name,
                creditorId: debt.creditorId,
                amount: debt.amount.toString(),
                type: debt.type,
                interestRate: debt.interestRate.toString(),
              })),
              assets: (fullCase.assets ?? []).map((asset) => ({
                id: asset.id,
                name: asset.name,
                type: asset.type,
                value: asset.value.toString(),
                description: '',
                location: '',
                registrationNumber: '',
                encumbered: false,
                encumbranceDetails: '',
              })),
              phase: fullCase.phase ?? 'Inicial',
              hearingDate: fullCase.hearingDate
                ? fullCase.hearingDate.toISOString().split('T')[0]
                : null,
              tramite: fullCase.tramite ?? '',
              filingDate: fullCase.filingDate
                ? fullCase.filingDate.toISOString().split('T')[0]
                : '',
              debtorIdNumber: fullCase.debtorIdNumber ?? '',
              convened: fullCase.convened ?? '',
              attorney: fullCase.attorney ?? '',
              owedCapital: fullCase.owedCapital?.toString() ?? '',
              designatedOperator: fullCase.designatedOperator ?? '',
              designationDate: fullCase.designationDate
                ? fullCase.designationDate.toISOString().split('T')[0]
                : '',
              positionAcceptanceDate: fullCase.positionAcceptanceDate
                ? fullCase.positionAcceptanceDate.toISOString().split('T')[0]
                : '',
              inadmissionDate: fullCase.inadmissionDate
                ? fullCase.inadmissionDate.toISOString().split('T')[0]
                : '',
              admissionDate: fullCase.admissionDate
                ? fullCase.admissionDate.toISOString().split('T')[0]
                : '',
              firstHearingDate: fullCase.firstHearingDate
                ? fullCase.firstHearingDate.toISOString().split('T')[0]
                : '',
              firstHearingTime: fullCase.firstHearingTime ?? '',
              rejection: fullCase.rejection ?? false,
              withdrawal: fullCase.withdrawal ?? false,
              hasLegalProcesses: fullCase.hasLegalProcesses ?? false,
              courtNumber: fullCase.courtNumber ?? '',
              city: fullCase.city ?? '',
              processType: fullCase.processType ?? '',
              plaintiff: fullCase.plaintiff ?? '',
              judicialFileNumber: fullCase.judicialFileNumber ?? '',
              suspensionDate: fullCase.suspensionDate
                ? fullCase.suspensionDate.toISOString().split('T')[0]
                : '',
              resultDeliveryDate: fullCase.resultDeliveryDate
                ? fullCase.resultDeliveryDate.toISOString().split('T')[0]
                : '',
              resultType: fullCase.resultType ?? '',
              resultDate: fullCase.resultDate
                ? fullCase.resultDate.toISOString().split('T')[0]
                : '',
              siccacNumber: fullCase.siccacNumber ?? '',
              riskCenterCommunication:
                fullCase.riskCenterCommunication ?? false,
            });
            setInitializedCaseId(caseData.id);
          }
        } catch {
          toast.error('Error al cargar los datos');
        } finally {
          setDataLoading(false);
        }
      };
      void loadData();
    }
  }, [open, caseData, form, initializedCaseId]);

  useEffect(() => {
    if (!showCreateDebtorDialog && open) {
      const reloadDebtors = async () => {
        try {
          const [debtorsData] = await getAllDebtors();
          setDebtors((debtorsData || []) as DebtorOption[]);
        } catch {}
      };
      void reloadDebtors();
    }
  }, [showCreateDebtorDialog, open]);

  useEffect(() => {
    if (selectedDebtorId && debtors.length > 0) {
      const debtor = debtors.find(
        (d: DebtorOption) => d.id === selectedDebtorId,
      );
      if (debtor) {
        setSelectedDebtor(debtor);

        form.setValue('debtorId', debtor.id);
      }
    }
  }, [selectedDebtorId, debtors, form]);

  const insolvencyCauses = [
    'Sobreendeudamiento por falta de recursos',
    'Pérdida de empleo',
    'Divorcio',
    'Enfermedad',
    'Accidente',
    'Estafa',
    'Disminución de ingresos',
    'Falta de educación financiera',
    'Muerte de cónyuge',
    'Nuevos créditos para cubrir otros',
    'Mala inversión',
  ];

  const handleCauseChange = (cause: string) => {
    const currentCauses = form.getValues('causes') ?? [];
    const newCauses = currentCauses.includes(cause)
      ? currentCauses.filter((c: string) => c !== cause)
      : [...currentCauses, cause];
    form.setValue('causes', newCauses);
  };

  const handleSubmit = (data: UpdateCaseFormData) => {
    const creditorsCount = data.debts?.length ?? caseData.creditors;

    const submitData = {
      id: caseData.id,
      debtorId: data.debtorId,
      type: data.type,
      status: data.status,
      totalDebt: data.totalDebt,
      creditors: creditorsCount,
      operatorId: data.operatorId,
      hearingDate: data.hearingDate,
      phase: data.phase ?? undefined,
      causes: data.causes,
      tramite: data.tramite,
      filingDate: data.filingDate,
      debtorIdNumber: data.debtorIdNumber,
      convened: data.convened,
      attorney: data.attorney,
      owedCapital: data.owedCapital,
      designatedOperator: data.designatedOperator,
      designationDate: data.designationDate,
      positionAcceptanceDate: data.positionAcceptanceDate,
      inadmissionDate: data.inadmissionDate,
      admissionDate: data.admissionDate,
      firstHearingDate: data.firstHearingDate,
      firstHearingTime: data.firstHearingTime,
      rejection: data.rejection,
      withdrawal: data.withdrawal,
      hasLegalProcesses: data.hasLegalProcesses,
      courtNumber: data.courtNumber,
      city: data.city,
      processType: data.processType,
      plaintiff: data.plaintiff,
      judicialFileNumber: data.judicialFileNumber,
      suspensionDate: data.suspensionDate,
      resultDeliveryDate: data.resultDeliveryDate,
      resultType: data.resultType,
      resultDate: data.resultDate,
      siccacNumber: data.siccacNumber,
      riskCenterCommunication: data.riskCenterCommunication,
    };

    executeUpdateCase(submitData);
  };

  const handleCreateDebtor = async () => {
    if (
      !newDebtorForm.name ||
      !newDebtorForm.idNumber ||
      !newDebtorForm.email ||
      !newDebtorForm.phone
    ) {
      toast.error('Por favor complete todos los campos requeridos');
      return;
    }

    setCreatingDebtor(true);
    try {
      const [result, error] = await createDebtor({
        ...newDebtorForm,
        idType: 'CC',
        occupation: 'Empleado',
        birthDate: undefined,
        dependents: 0,
        maritalStatus: 'SOLTERO',
        educationLevel: 'UNIVERSITARIO',
        emergencyContact: '',
        emergencyPhone: '',
        bankAccount: '',
        bankName: '',
        accountType: 'AHORROS',
        description: '',
      });

      if (error) {
        toast.error(error.message ?? 'Error al crear el deudor');
      } else {
        toast.success('Deudor creado exitosamente');
        setShowCreateDebtorDialog(false);
        setNewDebtorForm({
          name: '',
          idNumber: '',
          email: '',
          phone: '',
          address: '',
          city: 'Bogotá',
          department: 'Cundinamarca',
          monthlyIncome: 0,
          monthlyExpenses: 0,
        });
        const [debtorsData] = await getAllDebtors();
        setDebtors(
          (debtorsData || []).map((debtor) => ({
            id: debtor.id,
            name: debtor.name,
            idNumber: debtor.idNumber,
            email: debtor.email,
            phone: debtor.phone,
            address: debtor.address,
            city: debtor.city,
            department: debtor.department,
            monthlyIncome: Number(debtor.monthlyIncome),
            monthlyExpenses: Number(debtor.monthlyExpenses ?? 0),
          })),
        );
        if (result?.id) {
          setSelectedDebtorId(result.id);
        }
      }
    } catch {
      toast.error('Error al crear el deudor');
    } finally {
      setCreatingDebtor(false);
    }
  };

  useEffect(() => {
    if (!open) {
      setSelectedDebtorId('');
      setSelectedDebtor(null);
      form.reset();
      setInitializedCaseId(null);
      setNewDebtorForm({
        name: '',
        idNumber: '',
        email: '',
        phone: '',
        address: '',
        city: 'Bogotá',
        department: 'Cundinamarca',
        monthlyIncome: 0,
        monthlyExpenses: 0,
      });
    }
  }, [open, form]);

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-6xl">
          <DialogHeader>
            <DialogTitle>Editar Caso {caseData.caseNumber}</DialogTitle>
            <DialogDescription>
              Modifique la información del caso de {caseData.debtorName}. Los
              cambios se aplicarán inmediatamente.
            </DialogDescription>
          </DialogHeader>

          <FormProvider {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)}>
              <Tabs defaultValue="debtor" className="space-y-4">
                <TabsList className="grid w-full grid-cols-6">
                  <TabsTrigger value="debtor">Deudor</TabsTrigger>
                  <TabsTrigger value="case-details">Detalles</TabsTrigger>
                  <TabsTrigger value="legal-process">Proceso Legal</TabsTrigger>
                  <TabsTrigger value="creditors">Acreedores</TabsTrigger>
                  <TabsTrigger value="assets">Bienes</TabsTrigger>
                  <TabsTrigger value="documents">Documentos</TabsTrigger>
                </TabsList>

                <TabsContent value="debtor" className="space-y-4">
                  <DebtorSection
                    debtors={debtors}
                    selectedDebtorId={selectedDebtorId}
                    selectedDebtor={selectedDebtor}
                    insolvencyCauses={insolvencyCauses}
                    onDebtorSelect={setSelectedDebtorId}
                    onCreateDebtorClick={() => setShowCreateDebtorDialog(true)}
                    onCauseChange={handleCauseChange}
                    loading={dataLoading}
                  />
                </TabsContent>

                <TabsContent value="case-details" className="space-y-4">
                  <CaseDetailsSection />
                </TabsContent>

                <TabsContent value="legal-process" className="space-y-4">
                  <LegalProcessSection />
                </TabsContent>

                <TabsContent value="creditors" className="space-y-4">
                  <CreditorsTab caseId={caseData.id} creditors={creditors} />
                </TabsContent>

                <TabsContent value="assets" className="space-y-4">
                  <AssetsSection />
                </TabsContent>

                <TabsContent value="documents" className="space-y-4">
                  <DocumentsSection />
                </TabsContent>
              </Tabs>

              <DialogFooter>
                <DialogClose asChild>
                  <Button variant="outline">Cancelar</Button>
                </DialogClose>
                <Button type="submit" disabled={isPending}>
                  {isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Guardando Cambios...
                    </>
                  ) : (
                    'Guardar Cambios'
                  )}
                </Button>
              </DialogFooter>
            </form>
          </FormProvider>
        </DialogContent>
      </Dialog>

      <CreateDebtorDialog
        open={showCreateDebtorDialog}
        onOpenChange={setShowCreateDebtorDialog}
        newDebtorForm={newDebtorForm}
        creatingDebtor={creatingDebtor}
        onFormChange={setNewDebtorForm}
        onSubmit={handleCreateDebtor}
      />
    </>
  );
}
