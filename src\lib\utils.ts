import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatCurrency(value: number | string): string {
  const numericValue =
    typeof value === 'string' ? parseFloat(value) || 0 : value;
  return `$${numericValue.toLocaleString('es-CO')}`;
}

export function parseCurrencyInput(value: string): string {
  return value.replace(/[^\d]/g, '');
}
