{"version": "0.2.0", "configurations": [{"name": "Next.js: debug server-side", "type": "node", "request": "attach", "port": 9232, "restart": true, "localRoot": "${workspaceFolder}", "remoteRoot": "${workspaceFolder}", "skipFiles": ["<node_internals>/**"]}, {"name": "Next.js: debug client-side", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}", "skipFiles": ["<node_internals>/**", "**/node_modules/**"]}, {"name": "Next.js: debug full stack", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/next/dist/bin/next", "args": ["dev"], "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"], "serverReadyAction": {"pattern": "- Local:.+(https?://.+)", "uriFormat": "%s", "action": "debugWithChrome"}, "env": {"NODE_OPTIONS": "--inspect"}}]}