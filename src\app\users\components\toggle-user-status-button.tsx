'use client';

import { Loader2 } from 'lucide-react';
import { useServerAction } from 'zsa-react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { toggleUserStatus } from '@/features/user/actions';

import type { User } from '@/features/user/schemas';

interface ToggleUserStatusButtonProps {
  user: User;
  onOpenChange: (open: boolean) => void;
}

export function ToggleUserStatusButton({
  user,
  onOpenChange,
}: Readonly<ToggleUserStatusButtonProps>) {
  const { execute, isPending } = useServerAction(toggleUserStatus, {
    onSuccess: ({ data: { status, name } }) => {
      toast.success('Estado del usuario actualizado', {
        description: `El usuario ${name} ha sido ${status === 'Activo' ? 'activado' : 'desactivado'} correctamente`,
      });
      onOpenChange(false);
    },
    onError: ({ err: { message } }) => {
      toast.error(message || 'Error al cambiar el estado del usuario');
    },
  });

  const statusAction = user.status === 'Activo' ? 'Desactivar' : 'Activar';
  const loadingAction = user.status === 'Activo' ? 'Desactivando' : 'Activando';

  return (
    <Button
      variant="outline"
      onClick={() => execute(user.id)}
      disabled={isPending}
      className="w-full sm:w-auto"
    >
      {isPending ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          {`${loadingAction} Usuario...`}
        </>
      ) : (
        `${statusAction} Usuario`
      )}
    </Button>
  );
}
