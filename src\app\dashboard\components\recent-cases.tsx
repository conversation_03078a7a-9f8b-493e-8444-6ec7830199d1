'use client';

import { <PERSON>, <PERSON>, MoreHorizontal } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface Case {
  id: string;
  debtor: string;
  type: string;
  status: string;
  amount: string;
  date: string;
  creditors: number;
}

export function RecentCases() {
  const cases: Case[] = [
    {
      id: 'INS-2025-001',
      debtor: 'María González Pérez',
      type: 'Insolvencia',
      status: 'En negociación',
      amount: '$45,000,000',
      date: '2025-01-15',
      creditors: 5,
    },
    {
      id: 'CON-2025-002',
      debtor: 'Carlos Rodríguez Silva',
      type: 'Conciliación',
      status: 'Audiencia programada',
      amount: '$28,500,000',
      date: '2025-01-14',
      creditors: 3,
    },
    {
      id: 'ACU-2025-003',
      debtor: 'Ana Martínez López',
      type: 'Acuerdo de Apoyo',
      status: 'Documentos pendientes',
      amount: '$15,200,000',
      date: '2025-01-13',
      creditors: 2,
    },
    {
      id: 'INS-2025-004',
      debtor: 'Luis Fernando Castro',
      type: 'Insolvencia',
      status: 'Admitido',
      amount: '$67,800,000',
      date: '2025-01-12',
      creditors: 8,
    },
    {
      id: 'CON-2025-005',
      debtor: 'Patricia Herrera Gómez',
      type: 'Conciliación',
      status: 'Completado',
      amount: '$22,100,000',
      date: '2025-01-11',
      creditors: 4,
    },
  ];

  const [selectedCase, setSelectedCase] = useState<Case | null>(null);
  const [showCaseDetails, setShowCaseDetails] = useState(false);
  const [showEditCase, setShowEditCase] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completado':
        return 'bg-green-100 text-green-800';
      case 'En negociación':
        return 'bg-blue-100 text-blue-800';
      case 'Audiencia programada':
        return 'bg-purple-100 text-purple-800';
      case 'Documentos pendientes':
        return 'bg-orange-100 text-orange-800';
      case 'Admitido':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Insolvencia':
        return 'bg-red-100 text-red-800';
      case 'Conciliación':
        return 'bg-blue-100 text-blue-800';
      case 'Acuerdo de Apoyo':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleViewDetails = (case_: Case) => {
    setSelectedCase(case_);
    setShowCaseDetails(true);
  };

  const handleEditCase = (case_: Case) => {
    setSelectedCase(case_);
    setShowEditCase(true);
  };

  const handleSaveChanges = async () => {
    await new Promise((resolve) => setTimeout(resolve, 1000));

    toast.success('Los cambios se han guardado correctamente.');
    setShowEditCase(false);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Casos Recientes</CardTitle>
        <CardDescription>
          Últimos casos ingresados al sistema con su estado actual
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID Caso</TableHead>
                <TableHead>Deudor</TableHead>
                <TableHead>Tipo</TableHead>
                <TableHead>Estado</TableHead>
                <TableHead>Monto</TableHead>
                <TableHead>Acreedores</TableHead>
                <TableHead>Fecha</TableHead>
                <TableHead className="text-right">Acciones</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {cases.map((case_) => (
                <TableRow key={case_.id}>
                  <TableCell className="font-medium">{case_.id}</TableCell>
                  <TableCell>{case_.debtor}</TableCell>
                  <TableCell>
                    <Badge className={getTypeColor(case_.type)}>
                      {case_.type}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(case_.status)}>
                      {case_.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{case_.amount}</TableCell>
                  <TableCell>{case_.creditors}</TableCell>
                  <TableCell>
                    {new Date(case_.date).toLocaleDateString('es-CO')}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => handleViewDetails(case_)}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          Ver detalles
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditCase(case_)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Editar
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>

      {showCaseDetails && selectedCase && (
        <Dialog open={showCaseDetails} onOpenChange={setShowCaseDetails}>
          <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Detalles del Caso</DialogTitle>
              <DialogDescription>
                Información detallada del caso seleccionado.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-500">
                    ID Caso
                  </Label>
                  <p className="text-sm">{selectedCase.id}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">
                    Tipo
                  </Label>
                  <p className="text-sm">{selectedCase.type}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">
                    Deudor
                  </Label>
                  <p className="text-sm">{selectedCase.debtor}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">
                    Estado
                  </Label>
                  <p className="text-sm">{selectedCase.status}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">
                    Monto
                  </Label>
                  <p className="text-sm">{selectedCase.amount}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">
                    Acreedores
                  </Label>
                  <p className="text-sm">{selectedCase.creditors}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-500">
                    Fecha
                  </Label>
                  <p className="text-sm">
                    {new Date(selectedCase.date).toLocaleDateString('es-CO')}
                  </p>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowCaseDetails(false)}
              >
                Cerrar
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {showEditCase && selectedCase && (
        <Dialog open={showEditCase} onOpenChange={setShowEditCase}>
          <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Editar Caso</DialogTitle>
              <DialogDescription>
                Modifica la información del caso.
              </DialogDescription>
            </DialogHeader>
            <form
              className="space-y-4"
              onSubmit={(e) => {
                e.preventDefault();
                void handleSaveChanges();
              }}
            >
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">
                    Deudor
                  </Label>
                  <Input type="text" defaultValue={selectedCase.debtor} />
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">
                    Tipo
                  </Label>
                  <Select defaultValue={selectedCase.type}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Selecciona un tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Insolvencia">Insolvencia</SelectItem>
                      <SelectItem value="Conciliación">Conciliación</SelectItem>
                      <SelectItem value="Acuerdo de Apoyo">
                        Acuerdo de Apoyo
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">
                    Estado
                  </Label>
                  <Select defaultValue={selectedCase.status}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Selecciona un estado" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="En negociación">
                        En negociación
                      </SelectItem>
                      <SelectItem value="Audiencia programada">
                        Audiencia programada
                      </SelectItem>
                      <SelectItem value="Documentos pendientes">
                        Documentos pendientes
                      </SelectItem>
                      <SelectItem value="Admitido">Admitido</SelectItem>
                      <SelectItem value="Completado">Completado</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">
                    Monto
                  </Label>
                  <Input type="text" defaultValue={selectedCase.amount} />
                </div>
              </div>
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowEditCase(false)}
                >
                  Cancelar
                </Button>
                <Button type="submit">Guardar Cambios</Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      )}
    </Card>
  );
}
