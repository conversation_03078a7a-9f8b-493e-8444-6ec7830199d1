{"version": "2.0.0", "tasks": [{"label": "Next.js: Start Development Server", "type": "shell", "command": "npm", "args": ["run", "dev"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": {"owner": "nextjs", "pattern": {"regexp": "^(.*)$", "file": 1}, "background": {"activeOnStart": true, "beginsPattern": "^.*Starting.*", "endsPattern": "^.*Ready in.*"}}, "options": {"cwd": "${workspaceFolder}"}}, {"label": "Next.js: Start Development Server with Debugging", "type": "shell", "command": "npm", "args": ["run", "dev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": {"owner": "nextjs", "pattern": {"regexp": "^(.*)$", "file": 1}, "background": {"activeOnStart": true, "beginsPattern": "^.*Starting.*", "endsPattern": "^.*Ready in.*"}}, "options": {"cwd": "${workspaceFolder}", "env": {"NODE_OPTIONS": "--inspect=9231"}}}]}