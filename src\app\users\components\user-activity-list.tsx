'use client';

import { UserCheck } from 'lucide-react';

import { Badge } from '@/components/ui/badge';

const activities = [
  {
    user: '<PERSON><PERSON>',
    action: 'Generó Auto de Admisión',
    case: 'INS-2025-001',
    time: '2025-01-29 08:30',
    type: 'document',
  },
  {
    user: '<PERSON><PERSON>',
    action: 'Consultó reporte financiero',
    case: '<PERSON><PERSON><PERSON>',
    time: '2025-01-29 07:45',
    type: 'report',
  },
  {
    user: '<PERSON>',
    action: 'Subió documento REDAM',
    case: 'CON-2025-002',
    time: '2025-01-28 16:20',
    type: 'upload',
  },
  {
    user: '<PERSON><PERSON>',
    action: 'Programó audiencia',
    case: 'INS-2025-001',
    time: '2025-01-28 14:15',
    type: 'schedule',
  },
  {
    user: '<PERSON>',
    action: 'Inició sesión',
    case: '<PERSON><PERSON><PERSON>',
    time: '2025-01-25 14:15',
    type: 'login',
  },
];

export function UserActivityList() {
  return (
    <div className="space-y-4">
      {activities.map((activity, index) => (
        <div
          key={index}
          className="flex items-center justify-between rounded-lg border p-4"
        >
          <div className="flex items-center space-x-4">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
              <UserCheck className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="font-medium">{activity.user}</p>
              <p className="text-sm text-gray-600">
                {activity.action} - {activity.case}
              </p>
            </div>
          </div>
          <div className="text-right">
            <span className="text-sm text-gray-500">
              {new Date(activity.time).toLocaleString('es-CO')}
            </span>
            <Badge variant="outline" className="ml-2 text-xs">
              {activity.type}
            </Badge>
          </div>
        </div>
      ))}
    </div>
  );
}
