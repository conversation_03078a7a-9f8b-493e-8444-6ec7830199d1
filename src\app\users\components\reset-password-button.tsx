'use client';

import { Key, Loader2 } from 'lucide-react';
import { useServerAction } from 'zsa-react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { resetUserPassword } from '@/features/user/actions';

import type { User } from '@/features/user/schemas';

interface ResetPasswordButtonProps {
  user: User;
  onOpenChange: (open: boolean) => void;
}

export function ResetPasswordButton({
  user,
  onOpenChange,
}: Readonly<ResetPasswordButtonProps>) {
  const { execute, isPending } = useServerAction(resetUserPassword, {
    onSuccess: ({ data: { tempPassword, name } }) => {
      toast.success('Contraseña restablecida exitosamente', {
        description: `Nueva contraseña temporal para ${name}: ${tempPassword}`,
      });
      onOpenChange(false);
    },
    onError: ({ err: { message } }) => {
      toast.error(message || 'Error al restablecer la contraseña');
    },
  });

  return (
    <Button
      variant="secondary"
      onClick={() => execute(user.id)}
      disabled={isPending}
      className="w-full sm:w-auto"
    >
      {isPending ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Reseteando Contraseña...
        </>
      ) : (
        <>
          <Key className="mr-2 h-4 w-4" />
          Resetear Contraseña
        </>
      )}
    </Button>
  );
}
