import { getAllCases, getCaseStats } from '@/features/case/actions';

import { CasesContent } from './components/cases-content';

export default async function CasesPage() {
  const [[cases], [stats]] = await Promise.all([getAllCases(), getCaseStats()]);

  return (
    <CasesContent
      cases={cases || []}
      stats={
        stats || {
          total: 0,
          byStatus: [],
          byType: [],
          negotiation: 0,
          agreementApproved: 0,
          totalDebt: 0,
        }
      }
    />
  );
}
