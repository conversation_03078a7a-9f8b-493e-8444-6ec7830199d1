{"name": "insolventic", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "postinstall": "prisma generate", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "prepare": "husky", "db:seed": "prisma db seed", "db:reset": "tsx src/prisma/reset.ts", "db:reset:seed": "tsx src/prisma/reset.ts --seed"}, "prisma": {"schema": "src/prisma/schema.prisma", "seed": "tsx src/prisma/seed.ts"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{css,scss,md,json}": ["prettier --write"]}, "dependencies": {"@hookform/resolvers": "^5.2.0", "@prisma/client": "^6.12.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "docx": "^9.5.1", "docx-templates": "^4.14.1", "docxtemplater": "^3.65.2", "googleapis": "^154.0.0", "libreoffice-convert": "^1.6.1", "lucide-react": "^0.525.0", "mammoth": "^1.9.1", "next": "15.4.4", "next-themes": "^0.4.6", "pizzip": "^3.2.0", "react": "^19", "react-dom": "^19", "react-hook-form": "^7.61.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^4.0.10", "zsa": "^0.6.0", "zsa-react": "^0.2.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@types/bcrypt": "^6.0.0", "@types/node": "^24", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "9.32.0", "eslint-config-next": "15.4.4", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "prisma": "^6.12.0", "tailwindcss": "^4.1.11", "tsx": "^4.20.3", "typescript": "^5"}}