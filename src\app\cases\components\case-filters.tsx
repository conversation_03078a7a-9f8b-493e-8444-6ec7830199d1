'use client';

import { Search, Filter } from 'lucide-react';

import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface CaseFiltersProps {
  searchTerm: string;
  statusFilter: string;
  onSearchChange: (value: string) => void;
  onStatusChange: (value: string) => void;
}

export function CaseFilters({
  searchTerm,
  statusFilter,
  onSearchChange,
  onStatusChange,
}: Readonly<CaseFiltersProps>) {
  return (
    <div className="flex items-center space-x-2">
      <div className="relative">
        <Search className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
        <Input
          placeholder="Buscar por nombre, ID o cédula..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="w-80 pl-10"
        />
      </div>
      <Select value={statusFilter} onValueChange={onStatusChange}>
        <SelectTrigger className="w-48">
          <Filter className="mr-2 h-4 w-4" />
          <SelectValue placeholder="Filtrar por estado" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Todos los estados</SelectItem>
          <SelectItem value="En negociación">En negociación</SelectItem>
          <SelectItem value="Audiencia programada">
            Audiencia programada
          </SelectItem>
          <SelectItem value="Documentos pendientes">
            Documentos pendientes
          </SelectItem>
          <SelectItem value="Acuerdo aprobado">Acuerdo aprobado</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
