'use client';

import { Plus } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { useServerAction } from 'zsa-react';

import { getCaseById } from '@/features/case/actions';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';

import { DashboardHeader } from '../../dashboard/components/dashboard-header';

import { CaseCalendar } from './case-calendar';
import { CaseDetailsDialog } from './case-details-dialog';
import { CaseFilters } from './case-filters';
import { CaseStats } from './case-stats';
import { CaseTable } from './case-table';
import { CreateCaseDialog } from './create-case-dialog';
import { DeleteCaseDialog } from './delete-case-dialog';
import { EditCaseDialog } from './edit-case-dialog';

import type {
  Case,
  CaseStats as CaseStatsType,
  CaseWithRelations,
} from '@/features/case/schemas';

interface CasesContentProps {
  cases: Case[];
  stats: CaseStatsType;
}

export function CasesContent({ cases, stats }: Readonly<CasesContentProps>) {
  const [selectedCase, setSelectedCase] = useState<Case | null>(null);
  const [selectedCaseWithRelations, setSelectedCaseWithRelations] =
    useState<CaseWithRelations | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [loadingCaseDetails, setLoadingCaseDetails] = useState(false);

  const { execute: fetchCaseDetails } = useServerAction(getCaseById);

  const filteredCases = cases.filter((caseItem) => {
    const matchesSearch =
      caseItem.debtorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      caseItem.caseNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      caseItem.debtor.idNumber.includes(searchTerm);
    const matchesStatus =
      statusFilter === 'all' || caseItem.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const openCaseDetails = async (caseItem: Case) => {
    setSelectedCase(caseItem);
    setLoadingCaseDetails(true);
    setShowDetailsDialog(true);

    try {
      const [fullCase] = await fetchCaseDetails(caseItem.id);
      if (fullCase) {
        setSelectedCaseWithRelations(fullCase);
      }
    } catch (error) {
      toast.error('Error al cargar los detalles del caso');
      console.error('Error fetching case details:', error);
    } finally {
      setLoadingCaseDetails(false);
    }
  };

  const editCase = (caseItem: Case) => {
    setSelectedCase(caseItem);
    setShowEditDialog(true);
  };

  const deleteCase = (caseItem: Case) => {
    setSelectedCase(caseItem);
    setShowDeleteDialog(true);
  };

  const generateDocument = (caseItem: Case) => {
    toast.success(`Preparando documento para el caso ${caseItem.caseNumber}`);
  };

  const downloadCaseFile = (caseItem: Case) => {
    toast.success(`Descargando expediente del caso ${caseItem.caseNumber}`);
  };

  const markAsCompleted = (caseItem: Case) => {
    toast.success(
      `El caso ${caseItem.caseNumber} ha sido marcado como completado`,
    );
  };

  const handleCreateDialogChange = (open: boolean) => {
    setShowCreateDialog(open);
    if (!open) {
      setSelectedCase(null);
    }
  };

  const handleEditDialogChange = (open: boolean) => {
    setShowEditDialog(open);
    if (!open) {
      setSelectedCase(null);
    }
  };

  const handleDeleteDialogChange = (open: boolean) => {
    setShowDeleteDialog(open);
    if (!open) {
      setSelectedCase(null);
    }
  };

  const handleCaseCreated = () => {
    setShowCreateDialog(false);
  };

  const handleCaseUpdated = () => {
    setShowEditDialog(false);
    setSelectedCase(null);
  };

  const handleCaseDeleted = () => {
    setShowDeleteDialog(false);
    setSelectedCase(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader />

      <main className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Gestión de Casos
              </h1>
              <p className="text-gray-600">
                Administre casos de insolvencia, conciliación y acuerdos de
                apoyo
              </p>
            </div>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Nuevo Caso
            </Button>
          </div>

          <CaseStats stats={stats} />

          <Tabs defaultValue="list" className="space-y-4">
            <TabsList>
              <TabsTrigger value="list">Lista de Casos</TabsTrigger>
              <TabsTrigger value="calendar">Calendario</TabsTrigger>
            </TabsList>

            <TabsContent value="list" className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Casos Activos</CardTitle>
                      <CardDescription>
                        Lista completa de casos en el sistema
                      </CardDescription>
                    </div>
                    <CaseFilters
                      searchTerm={searchTerm}
                      statusFilter={statusFilter}
                      onSearchChange={setSearchTerm}
                      onStatusChange={setStatusFilter}
                    />
                  </div>
                </CardHeader>
                <CardContent>
                  <CaseTable
                    cases={filteredCases}
                    onViewDetails={openCaseDetails}
                    onEdit={editCase}
                    onDelete={deleteCase}
                    onGenerateDocument={generateDocument}
                    onDownload={downloadCaseFile}
                    onMarkComplete={markAsCompleted}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="calendar">
              <Card>
                <CardHeader>
                  <CardTitle>Calendario de Audiencias</CardTitle>
                  <CardDescription>
                    Vista de calendario con audiencias programadas
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <CaseCalendar cases={cases} onCaseClick={openCaseDetails} />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </main>

      {selectedCaseWithRelations && (
        <CaseDetailsDialog
          open={showDetailsDialog}
          onOpenChange={setShowDetailsDialog}
          case={selectedCaseWithRelations}
          loading={loadingCaseDetails}
        />
      )}

      <CreateCaseDialog
        open={showCreateDialog}
        onOpenChange={handleCreateDialogChange}
        onCaseCreated={handleCaseCreated}
      />

      {selectedCase && (
        <EditCaseDialog
          open={showEditDialog}
          onOpenChange={handleEditDialogChange}
          case={selectedCase}
          onCaseUpdated={handleCaseUpdated}
        />
      )}

      {selectedCase && (
        <DeleteCaseDialog
          case={selectedCase}
          open={showDeleteDialog}
          onOpenChange={handleDeleteDialogChange}
          onCaseDeleted={handleCaseDeleted}
        />
      )}
    </div>
  );
}
