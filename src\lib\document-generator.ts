import { Document, Packer, Paragraph, TextRun } from 'docx';
import * as mammoth from 'mammoth';
import { createReport } from 'docx-templates';
import { DocumentGenerationContext } from '@/features/document/types';

type PlaceholderType = {
  key: string;
  label: string;
};
type PlaceholderValue = string | number | boolean;

export class DocumentGenerator {
  /**
   * Genera un documento Word a partir de una plantilla y datos de contexto usando docx-templates
   */
  static async generateFromTemplate(
    templateBuffer: Buffer,
    context: DocumentGenerationContext,
    placeholderValues: Record<string, PlaceholderValue> = {},
  ): Promise<Buffer> {
    try {
      const report = await createReport({
        template: templateBuffer,
        data: {
          ...context,
          ...placeholderValues,
          currentDate: new Date().toLocaleDateString('es-ES'),
          currentYear: new Date().getFullYear(),
        },
        cmdDelimiter: ['{', '}'],
      });

      return Buffer.from(report);
    } catch (error) {
      console.error('Error generando documento:', error);
      throw new Error('Error al generar el documento');
    }
  }

  /**
   * Versión alternativa usando el método anterior (HTML) como fallback
   */
  static async generateFromTemplateHtml(
    templateBuffer: Buffer,
    context: DocumentGenerationContext,
    placeholderValues: Record<string, PlaceholderValue>,
  ): Promise<Buffer> {
    try {
      const result = await mammoth.convertToHtml({ buffer: templateBuffer });
      let htmlContent = result.value;

      htmlContent = this.replacePlaceholders(
        htmlContent,
        context,
        placeholderValues,
      );

      const wordBuffer = await this.htmlToDocx(htmlContent);

      return wordBuffer;
    } catch (error) {
      console.error('Error generando documento:', error);
      throw new Error('Error al generar el documento');
    }
  }

  /**
   * Reemplaza placeholders en el contenido HTML
   */
  private static replacePlaceholders(
    content: string,
    context: DocumentGenerationContext,
    customValues: Record<string, string | number | boolean>,
  ): string {
    let processedContent = content;

    processedContent = processedContent.replace(
      /\{\{caseNumber\}\}/g,
      context.case.caseNumber,
    );
    processedContent = processedContent.replace(
      /\{\{debtorName\}\}/g,
      context.case.debtorName,
    );
    processedContent = processedContent.replace(
      /\{\{caseType\}\}/g,
      context.case.type,
    );
    processedContent = processedContent.replace(
      /\{\{caseStatus\}\}/g,
      context.case.status,
    );
    processedContent = processedContent.replace(
      /\{\{totalDebt\}\}/g,
      context.case.totalDebt.toLocaleString('es-CO'),
    );
    processedContent = processedContent.replace(
      /\{\{creditors\}\}/g,
      context.case.creditors.toString(),
    );
    processedContent = processedContent.replace(
      /\{\{createdDate\}\}/g,
      context.case.createdDate.toLocaleDateString('es-CO'),
    );

    if (context.case.hearingDate) {
      processedContent = processedContent.replace(
        /\{\{hearingDate\}\}/g,
        context.case.hearingDate.toLocaleDateString('es-CO'),
      );
    }

    if (context.case.phase) {
      processedContent = processedContent.replace(
        /\{\{phase\}\}/g,
        context.case.phase,
      );
    }

    processedContent = processedContent.replace(
      /\{\{debtorDocumentNumber\}\}/g,
      context.debtor.documentNumber,
    );
    processedContent = processedContent.replace(
      /\{\{debtorDocumentType\}\}/g,
      context.debtor.documentType,
    );

    if (context.debtor.email) {
      processedContent = processedContent.replace(
        /\{\{debtorEmail\}\}/g,
        context.debtor.email,
      );
    }

    if (context.debtor.phone) {
      processedContent = processedContent.replace(
        /\{\{debtorPhone\}\}/g,
        context.debtor.phone,
      );
    }

    if (context.debtor.address) {
      processedContent = processedContent.replace(
        /\{\{debtorAddress\}\}/g,
        context.debtor.address,
      );
    }

    processedContent = processedContent.replace(
      /\{\{operatorName\}\}/g,
      context.operator.name,
    );
    processedContent = processedContent.replace(
      /\{\{operatorEmail\}\}/g,
      context.operator.email,
    );

    processedContent = processedContent.replace(
      /\{\{currentDate\}\}/g,
      new Date().toLocaleDateString('es-CO'),
    );
    processedContent = processedContent.replace(
      /\{\{currentDateTime\}\}/g,
      new Date().toLocaleString('es-CO'),
    );

    Object.entries(customValues).forEach(([key, value]) => {
      const placeholder = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      processedContent = processedContent.replace(
        placeholder,
        String(value || ''),
      );
    });

    return processedContent;
  }

  /**
   * Convierte HTML a documento Word
   */
  private static async htmlToDocx(htmlContent: string): Promise<Buffer> {
    const doc = new Document({
      sections: [
        {
          properties: {},
          children: [
            new Paragraph({
              children: [
                new TextRun({
                  text: htmlContent.replace(/<[^>]*>/g, ''),
                }),
              ],
            }),
          ],
        },
      ],
    });

    return await Packer.toBuffer(doc);
  }

  /**
   * Extrae placeholders de un documento Word (busca tanto {placeholder} como {{placeholder}})
   */
  static async extractPlaceholders(file: File): Promise<PlaceholderType[]> {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const result = await mammoth.extractRawText({ arrayBuffer });
      const text = result.value;

      const singleBraceRegex = /\{([^{}]+)\}/g;
      const doubleBraceRegex = /\{\{([^}]+)\}\}/g;

      const placeholders: PlaceholderType[] = [];

      const singleMatches = text.matchAll(singleBraceRegex);
      for (const match of singleMatches) {
        const key = match[1].trim();
        if (!placeholders.find((p) => p.key === key)) {
          placeholders.push({
            key,
            label: this.keyToLabel(key),
          });
        }
      }

      const doubleMatches = text.matchAll(doubleBraceRegex);
      for (const match of doubleMatches) {
        const key = match[1].trim();
        if (!placeholders.find((p) => p.key === key)) {
          placeholders.push({
            key,
            label: this.keyToLabel(key),
          });
        }
      }

      return placeholders;
    } catch (error) {
      console.error('Error extrayendo placeholders:', error);
      return this.extractPlaceholdersFromFilename(file.name);
    }
  }

  /**
   * Extrae placeholders básicos basados en el nombre del archivo como fallback
   */
  private static extractPlaceholdersFromFilename(
    filename: string,
  ): PlaceholderType[] {
    const commonPlaceholders: PlaceholderType[] = [
      {
        key: 'caseNumber',
        label: 'Número de Caso',
      },
      {
        key: 'debtorName',
        label: 'Nombre del Deudor',
      },
      {
        key: 'currentDate',
        label: 'Fecha Actual',
      },
      {
        key: 'operatorName',
        label: 'Nombre del Operador',
      },
    ];

    if (filename.toLowerCase().includes('audiencia')) {
      commonPlaceholders.push(
        {
          key: 'hearingDate',
          label: 'Fecha de Audiencia',
        },
        {
          key: 'hearingTime',
          label: 'Hora de Audiencia',
        },
      );
    }

    if (filename.toLowerCase().includes('acuerdo')) {
      commonPlaceholders.push(
        {
          key: 'paymentTerms',
          label: 'Términos de Pago',
        },
        {
          key: 'monthlyAmount',
          label: 'Cuota Mensual',
        },
      );
    }

    return commonPlaceholders;
  }

  /**
   * Convierte una clave de placeholder a una etiqueta legible
   */
  private static keyToLabel(key: string): string {
    const labels: Record<string, string> = {
      caseNumber: 'Número de Caso',
      debtorName: 'Nombre del Deudor',
      caseType: 'Tipo de Caso',
      caseStatus: 'Estado del Caso',
      totalDebt: 'Deuda Total',
      creditors: 'Número de Acreedores',
      createdDate: 'Fecha de Creación',
      hearingDate: 'Fecha de Audiencia',
      phase: 'Fase del Proceso',
      debtorDocumentNumber: 'Número de Documento del Deudor',
      debtorDocumentType: 'Tipo de Documento del Deudor',
      debtorEmail: 'Email del Deudor',
      debtorPhone: 'Teléfono del Deudor',
      debtorAddress: 'Dirección del Deudor',
      operatorName: 'Nombre del Operador',
      operatorEmail: 'Email del Operador',
      currentDate: 'Fecha Actual',
      currentDateTime: 'Fecha y Hora Actual',
    };

    return labels[key] || key.charAt(0).toUpperCase() + key.slice(1);
  }

  /**
   * Convierte un documento Word a HTML para edición
   */
  static async docxToHtml(wordBuffer: Buffer): Promise<string> {
    try {
      const result = await mammoth.convertToHtml({ buffer: wordBuffer });
      return result.value;
    } catch (error) {
      console.error('Error convirtiendo DOCX a HTML:', error);
      throw new Error('Error al convertir el documento para edición');
    }
  }
}
