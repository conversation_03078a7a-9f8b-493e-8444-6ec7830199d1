'use client';

import {
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  FileText,
  MapPin,
  Phone,
  Mail,
  User,
  Building,
  X,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';

interface CreditorResponse {
  name: string;
  amount: string;
  status: 'Pendiente' | 'Aprobado' | 'Rechazado';
}

interface NotificationDetails {
  caseId?: string;
  debtor?: string;
  hearingDate?: string;
  hearingTime?: string;
  location?: string;
  address?: string;
  judge?: string;
  type?: string;
  preparation?: string[];
  deadline?: string;
  daysRemaining?: number;
  task?: string;
  description?: string;
  creditors?: CreditorResponse[];
}

interface Notification {
  type:
    | 'audiencia'
    | 'documento'
    | 'vencimiento'
    | 'completado'
    | 'recordatorio';
  priority: 'high' | 'medium' | 'low';
  title: string;
  message: string;
  time: string | number | Date;
  read: boolean;
}

interface NotificationDetailsDialogProps {
  notification: Notification;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function NotificationDetailsDialog({
  notification,
  open,
  onOpenChange,
}: Readonly<NotificationDetailsDialogProps>) {
  if (!notification) return null;

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'Alta Prioridad';
      case 'medium':
        return 'Prioridad Media';
      case 'low':
        return 'Prioridad Baja';
      default:
        return 'Prioridad Normal';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'audiencia':
        return Calendar;
      case 'documento':
        return FileText;
      case 'vencimiento':
        return AlertTriangle;
      case 'completado':
        return CheckCircle;
      case 'recordatorio':
        return Clock;
      default:
        return Clock;
    }
  };

  const getNotificationDetails = (
    notification: Notification,
  ): NotificationDetails => {
    switch (notification.type) {
      case 'audiencia':
        return {
          caseId: 'INS-2025-001',
          debtor: 'María González Pérez',
          hearingDate: '29/01/2025',
          hearingTime: '10:00 AM',
          location: 'Juzgado 15 Civil del Circuito',
          address: 'Carrera 7 #12-85, Bogotá',
          judge: 'Dr. Carlos Mendoza',
          type: 'Audiencia de Insolvencia',
          preparation: [
            'Revisar documentos del caso',
            'Preparar alegatos iniciales',
            'Confirmar asistencia de testigos',
            'Verificar documentos de identidad',
          ],
        };
      case 'vencimiento':
        return {
          caseId: 'ACU-2025-003',
          debtor: 'Ana Martínez López',
          deadline: '31/01/2025',
          daysRemaining: 3,
          task: 'Respuesta de acreedores',
          description:
            'Los acreedores deben presentar su respuesta al acuerdo propuesto',
          creditors: [
            {
              name: 'Banco Nacional',
              amount: '$15,000,000',
              status: 'Pendiente',
            },
            {
              name: 'Cooperativa Financiera',
              amount: '$8,500,000',
              status: 'Aprobado',
            },
            {
              name: 'Tarjeta de Crédito XYZ',
              amount: '$2,300,000',
              status: 'Pendiente',
            },
          ],
        };
      default:
        return {};
    }
  };

  const TypeIcon = getTypeIcon(notification.type);
  const details: NotificationDetails = getNotificationDetails(notification);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
                <TypeIcon className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <DialogTitle className="text-xl">
                  {notification.title}
                </DialogTitle>
                <p className="mt-1 text-sm text-gray-600">
                  {notification.message}
                </p>
              </div>
            </div>
            <Badge className={getPriorityColor(notification.priority)}>
              {getPriorityText(notification.priority)}
            </Badge>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Clock className="h-5 w-5" />
                <span>Información General</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Fecha y Hora
                  </p>
                  <p className="text-sm">
                    {new Date(notification.time).toLocaleString('es-CO')}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Estado</p>
                  <Badge variant={notification.read ? 'secondary' : 'default'}>
                    {notification.read ? 'Leída' : 'No Leída'}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Tipo</p>
                  <p className="text-sm capitalize">{notification.type}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Prioridad</p>
                  <p className="text-sm">
                    {getPriorityText(notification.priority)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {notification.type === 'audiencia' && details.caseId && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5" />
                  <span>Detalles de la Audiencia</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <p className="flex items-center text-sm font-medium text-gray-600">
                      <FileText className="mr-2 h-4 w-4" />
                      Caso
                    </p>
                    <p className="text-sm font-semibold">{details.caseId}</p>
                  </div>
                  <div>
                    <p className="flex items-center text-sm font-medium text-gray-600">
                      <User className="mr-2 h-4 w-4" />
                      Deudor
                    </p>
                    <p className="text-sm">{details.debtor}</p>
                  </div>
                  <div>
                    <p className="flex items-center text-sm font-medium text-gray-600">
                      <Calendar className="mr-2 h-4 w-4" />
                      Fecha y Hora
                    </p>
                    <p className="text-sm">
                      {details.hearingDate} - {details.hearingTime}
                    </p>
                  </div>
                  <div>
                    <p className="flex items-center text-sm font-medium text-gray-600">
                      <Building className="mr-2 h-4 w-4" />
                      Juzgado
                    </p>
                    <p className="text-sm">{details.judge}</p>
                  </div>
                </div>

                <Separator />

                <div>
                  <p className="mb-2 flex items-center text-sm font-medium text-gray-600">
                    <MapPin className="mr-2 h-4 w-4" />
                    Ubicación
                  </p>
                  <p className="text-sm">{details.location}</p>
                  <p className="text-xs text-gray-500">{details.address}</p>
                </div>

                <Separator />

                <div>
                  <p className="mb-2 text-sm font-medium text-gray-600">
                    Preparación Requerida
                  </p>
                  <ul className="space-y-1">
                    {details.preparation?.map((item: string, index: number) => (
                      <li key={index} className="flex items-center text-sm">
                        <CheckCircle className="mr-2 h-3 w-3 text-green-600" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          )}

          {notification.type === 'vencimiento' && details.caseId && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <AlertTriangle className="h-5 w-5" />
                  <span>Detalles del Vencimiento</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <p className="flex items-center text-sm font-medium text-gray-600">
                      <FileText className="mr-2 h-4 w-4" />
                      Caso
                    </p>
                    <p className="text-sm font-semibold">{details.caseId}</p>
                  </div>
                  <div>
                    <p className="flex items-center text-sm font-medium text-gray-600">
                      <User className="mr-2 h-4 w-4" />
                      Deudor
                    </p>
                    <p className="text-sm">{details.debtor}</p>
                  </div>
                  <div>
                    <p className="flex items-center text-sm font-medium text-gray-600">
                      <Calendar className="mr-2 h-4 w-4" />
                      Fecha Límite
                    </p>
                    <p className="text-sm">{details.deadline}</p>
                  </div>
                  <div>
                    <p className="flex items-center text-sm font-medium text-gray-600">
                      <Clock className="mr-2 h-4 w-4" />
                      Días Restantes
                    </p>
                    <Badge variant="destructive">
                      {details.daysRemaining} días
                    </Badge>
                  </div>
                </div>

                <Separator />

                <div>
                  <p className="mb-2 text-sm font-medium text-gray-600">
                    Tarea Pendiente
                  </p>
                  <p className="text-sm font-semibold">{details.task}</p>
                  <p className="mt-1 text-xs text-gray-500">
                    {details.description}
                  </p>
                </div>

                {details.creditors && (
                  <>
                    <Separator />
                    <div>
                      <p className="mb-3 text-sm font-medium text-gray-600">
                        Estado de Acreedores
                      </p>
                      <div className="space-y-2">
                        {details.creditors.map(
                          (creditor: CreditorResponse, index: number) => (
                            <div
                              key={index}
                              className="flex items-center justify-between rounded-lg bg-gray-50 p-3"
                            >
                              <div>
                                <p className="text-sm font-medium">
                                  {creditor.name}
                                </p>
                                <p className="text-xs text-gray-500">
                                  {creditor.amount}
                                </p>
                              </div>
                              <Badge
                                variant={
                                  creditor.status === 'Aprobado'
                                    ? 'default'
                                    : 'secondary'
                                }
                              >
                                {creditor.status}
                              </Badge>
                            </div>
                          ),
                        )}
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>Acciones Disponibles</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {notification.type === 'audiencia' && (
                  <>
                    <Button size="sm">
                      <Calendar className="mr-2 h-4 w-4" />
                      Agregar al Calendario
                    </Button>
                    <Button size="sm" variant="outline">
                      <FileText className="mr-2 h-4 w-4" />
                      Ver Caso Completo
                    </Button>
                    <Button size="sm" variant="outline">
                      <Phone className="mr-2 h-4 w-4" />
                      Contactar Cliente
                    </Button>
                  </>
                )}
                {notification.type === 'vencimiento' && (
                  <>
                    <Button size="sm">
                      <AlertTriangle className="mr-2 h-4 w-4" />
                      Revisar Respuestas
                    </Button>
                    <Button size="sm" variant="outline">
                      <Mail className="mr-2 h-4 w-4" />
                      Enviar Recordatorio
                    </Button>
                    <Button size="sm" variant="outline">
                      <FileText className="mr-2 h-4 w-4" />
                      Ver Documentos
                    </Button>
                  </>
                )}
                <Button size="sm" variant="outline">
                  <X className="mr-2 h-4 w-4" />
                  Descartar Notificación
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
