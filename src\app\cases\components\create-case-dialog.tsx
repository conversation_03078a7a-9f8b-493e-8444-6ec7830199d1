'use client';

import { useState, useEffect } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { createCase } from '@/features/case/actions';
import { addDebtToCaseSchema } from '@/features/case/schemas';
import { getAllCreditors } from '@/features/creditor/actions';
import { getAllDebtors, createDebtor } from '@/features/debtor/actions';
import { getAllUsers } from '@/features/user/actions';
import { useServerAction } from 'zsa-react';
import {
  createCaseFormSchema,
  type CreateCaseFormData,
  type DebtorOption,
  type CreditorOption,
  type FormDebt,
  type AddDebtToCaseData,
  type NewDebtorForm,
} from '@/features/case/schemas';

import { AssetsSection } from './create-case-dialog/assets-section';
import { CreateDebtorDialog } from './create-case-dialog/create-debtor-dialog';
import { CreditorsSection } from './create-case-dialog/creditors-section';
import { DebtorSection } from './create-case-dialog/debtor-section';
import { DocumentsSection } from './create-case-dialog/documents-section';
import { CaseDetailsSection } from './create-case-dialog/case-details-section';
import { LegalProcessSection } from './create-case-dialog/legal-process-section';

interface CreateCaseDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCaseCreated?: () => void;
}

export function CreateCaseDialog({
  open,
  onOpenChange,
  onCaseCreated,
}: Readonly<CreateCaseDialogProps>) {
  const [dataLoading, setDataLoading] = useState(false);
  const [debtors, setDebtors] = useState<DebtorOption[]>([]);
  const [creditors, setCreditors] = useState<CreditorOption[]>([]);
  const [selectedDebtorId, setSelectedDebtorId] = useState<string>('');
  const [selectedDebtor, setSelectedDebtor] = useState<DebtorOption | null>(
    null,
  );
  const [showCreateDebtorDialog, setShowCreateDebtorDialog] = useState(false);
  const [newDebtorForm, setNewDebtorForm] = useState<NewDebtorForm>({
    name: '',
    idNumber: '',
    email: '',
    phone: '',
    address: '',
    city: 'Bogotá',
    department: 'Cundinamarca',
    monthlyIncome: 0,
    monthlyExpenses: 0,
  });
  const [creatingDebtor, setCreatingDebtor] = useState(false);

  const form = useForm({
    resolver: zodResolver(createCaseFormSchema),
    defaultValues: {
      debtorId: '',
      type: 'INSOLVENCY' as const,
      status: 'NEGOTIATION',
      totalDebt: '',
      creditors: 1,
      operatorId: '',
      causes: [],
      debts: [],
      assets: [],
      phase: 'Inicial',
      hearingDate: null,
      caseNumber: '',
    },
  });

  const { execute, isPending } = useServerAction(createCase, {
    onSuccess: () => {
      toast.success('Caso creado exitosamente');
      form.reset();
      onOpenChange(false);
      onCaseCreated?.();
    },
    onError: ({ err }) => {
      toast.error(err.message ?? 'Error al crear el caso');
    },
  });

  useEffect(() => {
    if (open) {
      form.reset({
        debtorId: '',
        type: 'INSOLVENCY' as const,
        status: 'NEGOTIATION',
        totalDebt: '',
        creditors: 1,
        operatorId: '',
        causes: [],
        debts: [],
        assets: [],
        phase: 'Inicial',
        hearingDate: null,
        caseNumber: '',
        tramite: '',
        filingDate: '',
        debtorIdNumber: '',
        convened: '',
        attorney: '',
        owedCapital: '',
        designatedOperator: '',
        designationDate: '',
        positionAcceptanceDate: '',
        inadmissionDate: '',
        admissionDate: '',
        firstHearingDate: '',
        firstHearingTime: '',
        rejection: false,
        withdrawal: false,
        hasLegalProcesses: false,
        courtNumber: '',
        city: '',
        processType: '',
        plaintiff: '',
        judicialFileNumber: '',
        suspensionDate: '',
        resultDeliveryDate: '',
        resultType: '',
        resultDate: '',
        siccacNumber: '',
        riskCenterCommunication: false,
      });

      const loadData = async () => {
        setDataLoading(true);
        try {
          const [[debtorsData], [creditorsData], [usersData, usersErr]] =
            await Promise.all([
              getAllDebtors(),
              getAllCreditors(),
              getAllUsers(),
            ]);

          if (usersErr) {
            toast.error('Error al cargar los usuarios');
            return;
          }
          setDebtors(
            debtorsData?.map((debtor) => ({
              id: debtor.id,
              name: debtor.name,
              idNumber: debtor.idNumber,
              email: debtor.email,
              phone: debtor.phone,
              address: debtor.address,
              city: debtor.city,
              department: debtor.department,
              monthlyIncome: Number(debtor.monthlyIncome),
              monthlyExpenses: Number(debtor.monthlyExpenses ?? 0),
            })) || [],
          );
          setCreditors(creditorsData as CreditorOption[]);

          const operatorUser = usersData.find(
            (user) =>
              user.role?.name === 'Operadora de Insolvencia' ||
              user.role?.name === 'Abogado',
          );
          if (operatorUser) {
            form.setValue('operatorId', operatorUser.id);
          }
        } catch {
          toast.error('Error al cargar los datos');
        } finally {
          setDataLoading(false);
        }
      };
      void loadData();
    }
  }, [open, form]);

  useEffect(() => {
    if (!showCreateDebtorDialog && open) {
      const reloadDebtors = async () => {
        try {
          const [debtorsData] = await getAllDebtors();
          setDebtors(
            debtorsData?.map((debtor) => ({
              id: debtor.id,
              name: debtor.name,
              idNumber: debtor.idNumber,
              email: debtor.email,
              phone: debtor.phone,
              address: debtor.address,
              city: debtor.city,
              department: debtor.department,
              monthlyIncome: Number(debtor.monthlyIncome),
              monthlyExpenses: Number(debtor.monthlyExpenses ?? 0),
            })) || [],
          );
        } catch {}
      };
      void reloadDebtors();
    }
  }, [showCreateDebtorDialog, open]);

  useEffect(() => {
    if (selectedDebtorId && debtors.length > 0) {
      const debtor = debtors.find((d) => d.id === selectedDebtorId);
      if (debtor) {
        setSelectedDebtor(debtor);
        form.setValue('debtorId', selectedDebtorId);
      }
    }
  }, [selectedDebtorId, debtors, form]);

  const newDebtForm = useForm<AddDebtToCaseData>({
    resolver: zodResolver(addDebtToCaseSchema),
    defaultValues: {
      caseId: '',
      creditorId: '',
      amount: '',
      type: '',
      interestRate: '',
    },
  });

  const insolvencyCauses = [
    'Sobreendeudamiento por falta de recursos',
    'Pérdida de empleo',
    'Divorcio',
    'Enfermedad',
    'Accidente',
    'Estafa',
    'Disminución de ingresos',
    'Falta de educación financiera',
    'Muerte de cónyuge',
    'Nuevos créditos para cubrir otros',
    'Mala inversión',
  ];

  const handleCauseChange = (cause: string) => {
    const currentCauses = form.getValues('causes') ?? [];
    const newCauses = currentCauses.includes(cause)
      ? currentCauses.filter((c) => c !== cause)
      : [...currentCauses, cause];
    form.setValue('causes', newCauses);
  };

  const addDebt = () => {
    const formData = newDebtForm.getValues();

    if (
      !formData.creditorId ||
      !formData.amount ||
      !formData.type ||
      !formData.interestRate
    ) {
      toast.error('Por favor complete todos los campos requeridos');
      return;
    }

    const currentDebts = form.getValues('debts') ?? [];
    const creditor = creditors.find((c) => c.id === formData.creditorId);
    const newDebtData = {
      creditor: creditor?.name ?? '',
      creditorId: formData.creditorId,
      amount: formData.amount,
      type: formData.type,
      interestRate: formData.interestRate,
      id: Date.now().toString(),
    };
    const updatedDebts = [...currentDebts, newDebtData];
    form.setValue('debts', updatedDebts);
    form.setValue('creditors', Math.max(1, updatedDebts.length));
    form.trigger('creditors');
    newDebtForm.reset({
      caseId: '',
      creditorId: '',
      amount: '',
      type: '',
      interestRate: '',
    });
    toast.success('Deuda agregada exitosamente');
  };

  const removeDebt = (debtId: string) => {
    const currentDebts = form.getValues('debts') ?? [];
    const updatedDebts = currentDebts.filter((debt) => debt.id !== debtId);
    form.setValue('debts', updatedDebts);
    form.setValue('creditors', Math.max(1, updatedDebts.length));
    form.trigger('creditors');
  };

  const handleSubmit = async (data: CreateCaseFormData) => {
    const isValid = await form.trigger();

    if (!isValid) {
      return;
    }

    if (!data.debtorId) {
      toast.error('Por favor seleccione un deudor');
      return;
    }

    if (!data.operatorId) {
      toast.error('Por favor seleccione un operador');
      return;
    }

    if (!data.totalDebt) {
      toast.error('Por favor ingrese la deuda total');
      return;
    }

    const creditorsCount = Math.max(1, data.debts?.length ?? 1);

    const submitData = {
      debtorId: data.debtorId,
      type: data.type,
      status: data.status,
      totalDebt: data.totalDebt,
      creditors: creditorsCount,
      operatorId: data.operatorId,
      hearingDate: data.hearingDate,
      phase: data.phase ?? undefined,
      caseNumber: data.caseNumber,
      causes: data.causes,
      tramite: data.tramite,
      filingDate: data.filingDate,
      debtorIdNumber: data.debtorIdNumber,
      convened: data.convened,
      attorney: data.attorney,
      owedCapital: data.owedCapital,
      designatedOperator: data.designatedOperator,
      designationDate: data.designationDate,
      positionAcceptanceDate: data.positionAcceptanceDate,
      inadmissionDate: data.inadmissionDate,
      admissionDate: data.admissionDate,
      firstHearingDate: data.firstHearingDate,
      firstHearingTime: data.firstHearingTime,
      rejection: data.rejection,
      withdrawal: data.withdrawal,
      hasLegalProcesses: data.hasLegalProcesses,
      courtNumber: data.courtNumber,
      city: data.city,
      processType: data.processType,
      plaintiff: data.plaintiff,
      judicialFileNumber: data.judicialFileNumber,
      suspensionDate: data.suspensionDate,
      resultDeliveryDate: data.resultDeliveryDate,
      resultType: data.resultType,
      resultDate: data.resultDate,
      siccacNumber: data.siccacNumber,
      riskCenterCommunication: data.riskCenterCommunication,
    };

    execute(submitData);
  };

  const handleCreateDebtor = async () => {
    if (
      !newDebtorForm.name ||
      !newDebtorForm.idNumber ||
      !newDebtorForm.email ||
      !newDebtorForm.phone
    ) {
      toast.error('Por favor complete todos los campos requeridos');
      return;
    }

    setCreatingDebtor(true);
    try {
      const [result, error] = await createDebtor({
        ...newDebtorForm,
        idType: 'CC',
        occupation: 'Empleado',
        birthDate: undefined,
        dependents: 0,
        maritalStatus: 'SOLTERO',
        educationLevel: 'UNIVERSITARIO',
        emergencyContact: '',
        emergencyPhone: '',
        bankAccount: '',
        bankName: '',
        accountType: 'AHORROS',
        description: '',
      });

      if (error) {
        toast.error(error.message ?? 'Error al crear el deudor');
      } else {
        toast.success('Deudor creado exitosamente');
        setShowCreateDebtorDialog(false);
        setNewDebtorForm({
          name: '',
          idNumber: '',
          email: '',
          phone: '',
          address: '',
          city: 'Bogotá',
          department: 'Cundinamarca',
          monthlyIncome: 0,
          monthlyExpenses: 0,
        });
        const [debtorsData] = await getAllDebtors();
        setDebtors(
          debtorsData?.map((debtor) => ({
            id: debtor.id,
            name: debtor.name,
            idNumber: debtor.idNumber,
            email: debtor.email,
            phone: debtor.phone,
            address: debtor.address,
            city: debtor.city,
            department: debtor.department,
            monthlyIncome: Number(debtor.monthlyIncome),
            monthlyExpenses: Number(debtor.monthlyExpenses ?? 0),
          })) || [],
        );
        if (result?.id) {
          setSelectedDebtorId(result.id);
        }
      }
    } catch {
      toast.error('Error al crear el deudor');
    } finally {
      setCreatingDebtor(false);
    }
  };

  useEffect(() => {
    if (!open) {
      setSelectedDebtorId('');
      setSelectedDebtor(null);
      setNewDebtorForm({
        name: '',
        idNumber: '',
        email: '',
        phone: '',
        address: '',
        city: 'Bogotá',
        department: 'Cundinamarca',
        monthlyIncome: 0,
        monthlyExpenses: 0,
      });
    }
  }, [open]);

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-6xl">
          <DialogHeader>
            <DialogTitle>Crear Nuevo Caso</DialogTitle>
            <DialogDescription>
              Complete la información para crear un nuevo caso de insolvencia
            </DialogDescription>
          </DialogHeader>

          <FormProvider {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)}>
              <Tabs defaultValue="debtor" className="space-y-4">
                <TabsList className="grid w-full grid-cols-6">
                  <TabsTrigger value="debtor">Deudor</TabsTrigger>
                  <TabsTrigger value="case-details">Detalles</TabsTrigger>
                  <TabsTrigger value="legal-process">Proceso Legal</TabsTrigger>
                  <TabsTrigger value="creditors">Acreedores</TabsTrigger>
                  <TabsTrigger value="assets">Bienes</TabsTrigger>
                  <TabsTrigger value="documents">Documentos</TabsTrigger>
                </TabsList>

                <TabsContent value="debtor" className="space-y-4">
                  <DebtorSection
                    debtors={debtors}
                    selectedDebtorId={selectedDebtorId}
                    selectedDebtor={selectedDebtor}
                    insolvencyCauses={insolvencyCauses}
                    onDebtorSelect={setSelectedDebtorId}
                    onCreateDebtorClick={() => setShowCreateDebtorDialog(true)}
                    onCauseChange={handleCauseChange}
                    loading={dataLoading}
                  />
                </TabsContent>

                <TabsContent value="case-details" className="space-y-4">
                  <CaseDetailsSection />
                </TabsContent>

                <TabsContent value="legal-process" className="space-y-4">
                  <LegalProcessSection />
                </TabsContent>

                <TabsContent value="creditors" className="space-y-4">
                  <CreditorsSection
                    creditors={creditors}
                    newDebt={{
                      creditor:
                        creditors.find(
                          (c) => c.id === newDebtForm.watch('creditorId'),
                        )?.name ?? '',
                      creditorId: newDebtForm.watch('creditorId'),
                      amount: newDebtForm.watch('amount'),
                      type: newDebtForm.watch('type'),
                      interestRate: newDebtForm.watch('interestRate'),
                    }}
                    debts={(form.watch('debts') ?? []) as FormDebt[]}
                    onNewDebtChange={(debt) => {
                      newDebtForm.setValue('creditorId', debt.creditorId ?? '');
                      newDebtForm.setValue('amount', debt.amount);
                      newDebtForm.setValue('type', debt.type);
                      newDebtForm.setValue('interestRate', debt.interestRate);
                    }}
                    onAddDebt={addDebt}
                    onRemoveDebt={removeDebt}
                  />
                </TabsContent>

                <TabsContent value="assets" className="space-y-4">
                  <AssetsSection />
                </TabsContent>

                <TabsContent value="documents" className="space-y-4">
                  <DocumentsSection />
                </TabsContent>
              </Tabs>

              <DialogFooter>
                <DialogClose asChild>
                  <Button variant="outline">Cancelar</Button>
                </DialogClose>
                <Button
                  type="submit"
                  disabled={isPending}
                  onClick={async () => {
                    const currentDebts = form.getValues('debts') ?? [];
                    const creditorsCount = Math.max(1, currentDebts.length);
                    form.setValue('creditors', creditorsCount);
                    await form.trigger();
                  }}
                >
                  {isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creando...
                    </>
                  ) : (
                    'Crear Caso'
                  )}
                </Button>
              </DialogFooter>
            </form>
          </FormProvider>
        </DialogContent>
      </Dialog>

      <CreateDebtorDialog
        open={showCreateDebtorDialog}
        onOpenChange={setShowCreateDebtorDialog}
        newDebtorForm={newDebtorForm}
        creatingDebtor={creatingDebtor}
        onFormChange={setNewDebtorForm}
        onSubmit={handleCreateDebtor}
      />
    </>
  );
}
